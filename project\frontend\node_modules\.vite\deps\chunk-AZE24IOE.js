import {
  VueInstance,
  _defineProperty,
  _toArray,
  config,
  merge,
  removeElement
} from "./chunk-MPDF5ICW.js";

// node_modules/buefy/dist/esm/trapFocus-f0736873.js
var findFocusable = function findFocusable2(element) {
  var programmatic = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
  if (!element) {
    return null;
  }
  if (programmatic) {
    return element.querySelectorAll('*[tabindex="-1"]');
  }
  return element.querySelectorAll('a[href]:not([tabindex="-1"]),\n                                     area[href],\n                                     input:not([disabled]),\n                                     select:not([disabled]),\n                                     textarea:not([disabled]),\n                                     button:not([disabled]),\n                                     iframe,\n                                     object,\n                                     embed,\n                                     *[tabindex]:not([tabindex="-1"]),\n                                     *[contenteditable]');
};
var onKeyDown;
var bind = function bind2(el, _ref) {
  var _ref$value = _ref.value, value = _ref$value === void 0 ? true : _ref$value;
  if (value) {
    var focusable = findFocusable(el);
    var focusableProg = findFocusable(el, true);
    if (focusable && focusable.length > 0) {
      onKeyDown = function onKeyDown2(event) {
        focusable = findFocusable(el);
        focusableProg = findFocusable(el, true);
        var firstFocusable = focusable[0];
        var lastFocusable = focusable[focusable.length - 1];
        if (event.target === firstFocusable && event.shiftKey && event.key === "Tab") {
          event.preventDefault();
          lastFocusable.focus();
        } else if ((event.target === lastFocusable || Array.from(focusableProg).indexOf(event.target) >= 0) && !event.shiftKey && event.key === "Tab") {
          event.preventDefault();
          firstFocusable.focus();
        }
      };
      el.addEventListener("keydown", onKeyDown);
    }
  }
};
var unbind = function unbind2(el) {
  el.removeEventListener("keydown", onKeyDown);
};
var directive = {
  bind,
  unbind
};
var trapFocus = directive;

// node_modules/buefy/dist/esm/plugins-218aea86.js
function normalizeComponent(template, style, script5, scopeId, isFunctionalTemplate, moduleIdentifier, shadowMode, createInjector, createInjectorSSR, createInjectorShadow) {
  if (typeof shadowMode !== "boolean") {
    createInjectorSSR = createInjector;
    createInjector = shadowMode;
    shadowMode = false;
  }
  const options = typeof script5 === "function" ? script5.options : script5;
  if (template && template.render) {
    options.render = template.render;
    options.staticRenderFns = template.staticRenderFns;
    options._compiled = true;
    if (isFunctionalTemplate) {
      options.functional = true;
    }
  }
  if (scopeId) {
    options._scopeId = scopeId;
  }
  let hook;
  if (moduleIdentifier) {
    hook = function(context) {
      context = context || // cached call
      this.$vnode && this.$vnode.ssrContext || // stateful
      this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext;
      if (!context && typeof __VUE_SSR_CONTEXT__ !== "undefined") {
        context = __VUE_SSR_CONTEXT__;
      }
      if (style) {
        style.call(this, createInjectorSSR(context));
      }
      if (context && context._registeredComponents) {
        context._registeredComponents.add(moduleIdentifier);
      }
    };
    options._ssrRegister = hook;
  } else if (style) {
    hook = shadowMode ? function(context) {
      style.call(this, createInjectorShadow(context, this.$root.$options.shadowRoot));
    } : function(context) {
      style.call(this, createInjector(context));
    };
  }
  if (hook) {
    if (options.functional) {
      const originalRender = options.render;
      options.render = function renderWithStyleInjection(h, context) {
        hook.call(context);
        return originalRender(h, context);
      };
    } else {
      const existing = options.beforeCreate;
      options.beforeCreate = existing ? [].concat(existing, hook) : [hook];
    }
  }
  return script5;
}
var use = function use2(plugin) {
  if (typeof window !== "undefined" && window.Vue) {
    window.Vue.use(plugin);
  }
};
var registerComponent = function registerComponent2(Vue, component) {
  Vue.component(component.name, component);
};
var registerComponentProgrammatic = function registerComponentProgrammatic2(Vue, property, component) {
  if (!Vue.prototype.$buefy) Vue.prototype.$buefy = {};
  Vue.prototype.$buefy[property] = component;
};

// node_modules/buefy/dist/esm/Icon-60d47b31.js
var mdiIcons = {
  sizes: {
    "default": "mdi-24px",
    "is-small": null,
    "is-medium": "mdi-36px",
    "is-large": "mdi-48px"
  },
  iconPrefix: "mdi-"
};
var faIcons = function faIcons2() {
  var faIconPrefix = config && config.defaultIconComponent ? "" : "fa-";
  return {
    sizes: {
      "default": null,
      "is-small": null,
      "is-medium": faIconPrefix + "lg",
      "is-large": faIconPrefix + "2x"
    },
    iconPrefix: faIconPrefix,
    internalIcons: {
      "information": "info-circle",
      "alert": "exclamation-triangle",
      "alert-circle": "exclamation-circle",
      "chevron-right": "angle-right",
      "chevron-left": "angle-left",
      "chevron-down": "angle-down",
      "eye-off": "eye-slash",
      "menu-down": "caret-down",
      "menu-up": "caret-up",
      "close-circle": "times-circle"
    }
  };
};
var getIcons = function getIcons2() {
  var icons = {
    mdi: mdiIcons,
    fa: faIcons(),
    fas: faIcons(),
    far: faIcons(),
    fad: faIcons(),
    fab: faIcons(),
    fal: faIcons(),
    "fa-solid": faIcons(),
    "fa-regular": faIcons(),
    "fa-light": faIcons(),
    "fa-thin": faIcons(),
    "fa-duotone": faIcons(),
    "fa-brands": faIcons()
  };
  if (config && config.customIconPacks) {
    icons = merge(icons, config.customIconPacks, true);
  }
  return icons;
};
var getIcons$1 = getIcons;
var script = {
  name: "BIcon",
  props: {
    type: [String, Object],
    component: String,
    pack: String,
    icon: String,
    size: String,
    customSize: String,
    customClass: String,
    both: Boolean
    // This is used internally to show both MDI and FA icon
  },
  computed: {
    iconConfig: function iconConfig() {
      var allIcons = getIcons$1();
      return allIcons[this.newPack];
    },
    iconPrefix: function iconPrefix() {
      if (this.iconConfig && this.iconConfig.iconPrefix) {
        return this.iconConfig.iconPrefix;
      }
      return "";
    },
    /**
    * Internal icon name based on the pack.
    * If pack is 'fa', gets the equivalent FA icon name of the MDI,
    * internal icons are always MDI.
    */
    newIcon: function newIcon() {
      return "".concat(this.iconPrefix).concat(this.getEquivalentIconOf(this.icon));
    },
    newPack: function newPack() {
      return this.pack || config.defaultIconPack;
    },
    newType: function newType() {
      if (!this.type) return;
      var splitType = [];
      if (typeof this.type === "string") {
        splitType = this.type.split("-");
      } else {
        for (var key in this.type) {
          if (this.type[key]) {
            splitType = key.split("-");
            break;
          }
        }
      }
      if (splitType.length <= 1) return;
      var _splitType = splitType, _splitType2 = _toArray(_splitType), type = _splitType2.slice(1);
      return "has-text-".concat(type.join("-"));
    },
    newCustomSize: function newCustomSize() {
      return this.customSize || this.customSizeByPack;
    },
    customSizeByPack: function customSizeByPack() {
      if (this.iconConfig && this.iconConfig.sizes) {
        if (this.size && this.iconConfig.sizes[this.size] !== void 0) {
          return this.iconConfig.sizes[this.size];
        } else if (this.iconConfig.sizes.default) {
          return this.iconConfig.sizes.default;
        }
      }
      return null;
    },
    useIconComponent: function useIconComponent() {
      return this.component || config.defaultIconComponent;
    }
  },
  methods: {
    /**
    * Equivalent icon name of the MDI.
    */
    getEquivalentIconOf: function getEquivalentIconOf(value) {
      if (!this.both) {
        return value;
      }
      if (this.iconConfig && this.iconConfig.internalIcons && this.iconConfig.internalIcons[value]) {
        return this.iconConfig.internalIcons[value];
      }
      return value;
    }
  }
};
var __vue_script__ = script;
var __vue_render__ = function() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("span", { staticClass: "icon", class: [_vm.newType, _vm.size] }, [!_vm.useIconComponent ? _c("i", { class: [_vm.newPack, _vm.newIcon, _vm.newCustomSize, _vm.customClass] }) : _c(_vm.useIconComponent, { tag: "component", class: [_vm.customClass], attrs: { "icon": [_vm.newPack, _vm.newIcon], "size": _vm.newCustomSize } })], 1);
};
var __vue_staticRenderFns__ = [];
var __vue_inject_styles__ = void 0;
var __vue_scope_id__ = void 0;
var __vue_module_identifier__ = void 0;
var __vue_is_functional_template__ = false;
var __vue_component__ = normalizeComponent(
  { render: __vue_render__, staticRenderFns: __vue_staticRenderFns__ },
  __vue_inject_styles__,
  __vue_script__,
  __vue_scope_id__,
  __vue_is_functional_template__,
  __vue_module_identifier__,
  false,
  void 0,
  void 0,
  void 0
);
var Icon = __vue_component__;

// node_modules/buefy/dist/esm/Modal-7da7641f.js
var script2 = {
  name: "BModal",
  directives: {
    trapFocus
  },
  // deprecated, to replace with default 'value' in the next breaking change
  model: {
    prop: "active",
    event: "update:active"
  },
  props: {
    active: Boolean,
    component: [Object, Function, String],
    content: [String, Array],
    programmatic: Boolean,
    props: Object,
    events: Object,
    width: {
      type: [String, Number],
      default: 960
    },
    hasModalCard: Boolean,
    animation: {
      type: String,
      default: "zoom-out"
    },
    canCancel: {
      type: [Array, Boolean],
      default: function _default() {
        return config.defaultModalCanCancel;
      }
    },
    onCancel: {
      type: Function,
      default: function _default2() {
      }
    },
    scroll: {
      type: String,
      default: function _default3() {
        return config.defaultModalScroll ? config.defaultModalScroll : "clip";
      },
      validator: function validator(value) {
        return ["clip", "keep"].indexOf(value) >= 0;
      }
    },
    fullScreen: Boolean,
    trapFocus: {
      type: Boolean,
      default: function _default4() {
        return config.defaultTrapFocus;
      }
    },
    autoFocus: {
      type: Boolean,
      default: function _default5() {
        return config.defaultAutoFocus;
      }
    },
    customClass: String,
    customContentClass: [String, Array, Object],
    ariaRole: {
      type: String,
      validator: function validator2(value) {
        return ["dialog", "alertdialog"].indexOf(value) >= 0;
      }
    },
    ariaModal: Boolean,
    ariaLabel: {
      type: String,
      validator: function validator3(value) {
        return Boolean(value);
      }
    },
    closeButtonAriaLabel: String,
    destroyOnHide: {
      type: Boolean,
      default: true
    },
    renderOnMounted: {
      type: Boolean,
      default: false
    }
  },
  data: function data() {
    return {
      isActive: this.active || false,
      savedScrollTop: null,
      newWidth: typeof this.width === "number" ? this.width + "px" : this.width,
      animating: !this.active,
      destroyed: !(this.active || this.renderOnMounted)
    };
  },
  computed: {
    cancelOptions: function cancelOptions() {
      return typeof this.canCancel === "boolean" ? this.canCancel ? config.defaultModalCanCancel : [] : this.canCancel;
    },
    showX: function showX() {
      return this.cancelOptions.indexOf("x") >= 0;
    },
    customStyle: function customStyle() {
      if (!this.fullScreen) {
        return {
          maxWidth: this.newWidth
        };
      }
      return null;
    }
  },
  watch: {
    active: function active(value) {
      this.isActive = value;
    },
    isActive: function isActive(value) {
      var _this = this;
      if (value) this.destroyed = false;
      this.handleScroll();
      this.$nextTick(function() {
        if (value && _this.$el && _this.$el.focus && _this.autoFocus) {
          _this.$el.focus();
        }
      });
    }
  },
  methods: {
    handleScroll: function handleScroll() {
      if (typeof window === "undefined") return;
      if (this.scroll === "clip") {
        if (this.isActive) {
          document.documentElement.classList.add("is-clipped");
        } else {
          document.documentElement.classList.remove("is-clipped");
        }
        return;
      }
      this.savedScrollTop = !this.savedScrollTop ? document.documentElement.scrollTop : this.savedScrollTop;
      if (this.isActive) {
        document.body.classList.add("is-noscroll");
      } else {
        document.body.classList.remove("is-noscroll");
      }
      if (this.isActive) {
        document.body.style.top = "-".concat(this.savedScrollTop, "px");
        return;
      }
      document.documentElement.scrollTop = this.savedScrollTop;
      document.body.style.top = null;
      this.savedScrollTop = null;
    },
    /**
    * Close the Modal if canCancel and call the onCancel prop (function).
    */
    cancel: function cancel(method) {
      if (this.cancelOptions.indexOf(method) < 0) return;
      this.$emit("cancel", arguments);
      this.onCancel.apply(null, arguments);
      this.close();
    },
    /**
    * Call the onCancel prop (function).
    * Emit events, and destroy modal if it's programmatic.
    */
    close: function close() {
      var _this2 = this;
      this.$emit("close");
      this.$emit("update:active", false);
      if (this.programmatic) {
        this.isActive = false;
        setTimeout(function() {
          _this2.$destroy();
          removeElement(_this2.$el);
        }, 150);
      }
    },
    /**
    * Keypress event that is bound to the document.
    */
    keyPress: function keyPress(_ref) {
      var key = _ref.key;
      if (this.isActive && (key === "Escape" || key === "Esc")) this.cancel("escape");
    },
    /**
    * Transition after-enter hook
    */
    afterEnter: function afterEnter() {
      this.animating = false;
      this.$emit("after-enter");
    },
    /**
    * Transition before-leave hook
    */
    beforeLeave: function beforeLeave() {
      this.animating = true;
    },
    /**
    * Transition after-leave hook
    */
    afterLeave: function afterLeave() {
      if (this.destroyOnHide) {
        this.destroyed = true;
      }
      this.$emit("after-leave");
    }
  },
  created: function created() {
    if (typeof window !== "undefined") {
      document.addEventListener("keyup", this.keyPress);
    }
  },
  beforeMount: function beforeMount() {
    this.programmatic && document.body.appendChild(this.$el);
  },
  mounted: function mounted() {
    if (this.programmatic) this.isActive = true;
    else if (this.isActive) this.handleScroll();
  },
  beforeDestroy: function beforeDestroy() {
    if (typeof window !== "undefined") {
      document.removeEventListener("keyup", this.keyPress);
      document.documentElement.classList.remove("is-clipped");
      var savedScrollTop = !this.savedScrollTop ? document.documentElement.scrollTop : this.savedScrollTop;
      document.body.classList.remove("is-noscroll");
      document.documentElement.scrollTop = savedScrollTop;
      document.body.style.top = null;
    }
  }
};
var __vue_script__2 = script2;
var __vue_render__2 = function() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("transition", { attrs: { "name": _vm.animation }, on: { "after-enter": _vm.afterEnter, "before-leave": _vm.beforeLeave, "after-leave": _vm.afterLeave } }, [!_vm.destroyed ? _c("div", { directives: [{ name: "show", rawName: "v-show", value: _vm.isActive, expression: "isActive" }, { name: "trap-focus", rawName: "v-trap-focus", value: _vm.trapFocus, expression: "trapFocus" }], staticClass: "modal is-active", class: [{ "is-full-screen": _vm.fullScreen }, _vm.customClass], attrs: { "tabindex": "-1", "role": _vm.ariaRole, "aria-label": _vm.ariaLabel, "aria-modal": _vm.ariaModal } }, [_c("div", { staticClass: "modal-background", on: { "click": function($event) {
    return _vm.cancel("outside");
  } } }), _c("div", { staticClass: "animation-content", class: [{ "modal-content": !_vm.hasModalCard }, _vm.customContentClass], style: _vm.customStyle }, [_vm.component ? _c(_vm.component, _vm._g(_vm._b({ tag: "component", attrs: { "can-cancel": _vm.canCancel }, on: { "close": _vm.close } }, "component", _vm.props, false), _vm.events)) : _vm.content ? [_c("div", { domProps: { "innerHTML": _vm._s(_vm.content) } })] : _vm._t("default", null, { "canCancel": _vm.canCancel, "close": _vm.close }), _vm.showX ? _c("button", { directives: [{ name: "show", rawName: "v-show", value: !_vm.animating, expression: "!animating" }], staticClass: "modal-close is-large", attrs: { "type": "button", "aria-label": _vm.closeButtonAriaLabel }, on: { "click": function($event) {
    return _vm.cancel("x");
  } } }) : _vm._e()], 2)]) : _vm._e()]);
};
var __vue_staticRenderFns__2 = [];
var __vue_inject_styles__2 = void 0;
var __vue_scope_id__2 = void 0;
var __vue_module_identifier__2 = void 0;
var __vue_is_functional_template__2 = false;
var __vue_component__2 = normalizeComponent(
  { render: __vue_render__2, staticRenderFns: __vue_staticRenderFns__2 },
  __vue_inject_styles__2,
  __vue_script__2,
  __vue_scope_id__2,
  __vue_is_functional_template__2,
  __vue_module_identifier__2,
  false,
  void 0,
  void 0,
  void 0
);
var Modal = __vue_component__2;

// node_modules/buefy/dist/esm/Button-521f6efc.js
var script3 = {
  name: "BButton",
  components: _defineProperty({}, Icon.name, Icon),
  inheritAttrs: false,
  props: {
    type: [String, Object],
    size: String,
    label: String,
    iconPack: String,
    iconLeft: String,
    iconRight: String,
    rounded: {
      type: Boolean,
      default: function _default6() {
        return config.defaultButtonRounded;
      }
    },
    loading: Boolean,
    outlined: Boolean,
    expanded: Boolean,
    inverted: Boolean,
    focused: Boolean,
    active: Boolean,
    hovered: Boolean,
    selected: Boolean,
    nativeType: {
      type: String,
      default: "button",
      validator: function validator4(value) {
        return ["button", "submit", "reset"].indexOf(value) >= 0;
      }
    },
    tag: {
      type: String,
      default: "button",
      validator: function validator5(value) {
        return config.defaultLinkTags.indexOf(value) >= 0;
      }
    }
  },
  computed: {
    computedTag: function computedTag() {
      if (this.$attrs.disabled !== void 0 && this.$attrs.disabled !== false) {
        return "button";
      }
      return this.tag;
    },
    iconSize: function iconSize() {
      if (!this.size || this.size === "is-medium") {
        return "is-small";
      } else if (this.size === "is-large") {
        return "is-medium";
      }
      return this.size;
    }
  }
};
var __vue_script__3 = script3;
var __vue_render__3 = function() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c(_vm.computedTag, _vm._g(_vm._b({ tag: "component", staticClass: "button", class: [_vm.size, _vm.type, {
    "is-rounded": _vm.rounded,
    "is-loading": _vm.loading,
    "is-outlined": _vm.outlined,
    "is-fullwidth": _vm.expanded,
    "is-inverted": _vm.inverted,
    "is-focused": _vm.focused,
    "is-active": _vm.active,
    "is-hovered": _vm.hovered,
    "is-selected": _vm.selected
  }], attrs: { "type": ["button", "input"].includes(_vm.computedTag) ? _vm.nativeType : void 0 } }, "component", _vm.$attrs, false), _vm.$listeners), [_vm.iconLeft ? _c("b-icon", { attrs: { "pack": _vm.iconPack, "icon": _vm.iconLeft, "size": _vm.iconSize } }) : _vm._e(), _vm.label ? _c("span", [_vm._v(_vm._s(_vm.label))]) : _vm.$slots.default ? _c("span", [_vm._t("default")], 2) : _vm._e(), _vm.iconRight ? _c("b-icon", { attrs: { "pack": _vm.iconPack, "icon": _vm.iconRight, "size": _vm.iconSize } }) : _vm._e()], 1);
};
var __vue_staticRenderFns__3 = [];
var __vue_inject_styles__3 = void 0;
var __vue_scope_id__3 = void 0;
var __vue_module_identifier__3 = void 0;
var __vue_is_functional_template__3 = false;
var __vue_component__3 = normalizeComponent(
  { render: __vue_render__3, staticRenderFns: __vue_staticRenderFns__3 },
  __vue_inject_styles__3,
  __vue_script__3,
  __vue_scope_id__3,
  __vue_is_functional_template__3,
  __vue_module_identifier__3,
  false,
  void 0,
  void 0,
  void 0
);
var Button = __vue_component__3;

// node_modules/buefy/dist/esm/dialog.js
var script4 = {
  name: "BDialog",
  components: _defineProperty(_defineProperty({}, Icon.name, Icon), Button.name, Button),
  directives: {
    trapFocus
  },
  extends: Modal,
  props: {
    title: String,
    message: [String, Array],
    icon: String,
    iconPack: String,
    hasIcon: Boolean,
    type: {
      type: String,
      default: "is-primary"
    },
    size: String,
    confirmText: {
      type: String,
      default: function _default7() {
        return config.defaultDialogConfirmText ? config.defaultDialogConfirmText : "OK";
      }
    },
    cancelText: {
      type: String,
      default: function _default8() {
        return config.defaultDialogCancelText ? config.defaultDialogCancelText : "Cancel";
      }
    },
    hasInput: Boolean,
    // Used internally to know if it's prompt
    inputAttrs: {
      type: Object,
      default: function _default9() {
        return {};
      }
    },
    onConfirm: {
      type: Function,
      default: function _default10() {
      }
    },
    closeOnConfirm: {
      type: Boolean,
      default: true
    },
    container: {
      type: String,
      default: function _default11() {
        return config.defaultContainerElement;
      }
    },
    focusOn: {
      type: String,
      default: "confirm"
    },
    trapFocus: {
      type: Boolean,
      default: function _default12() {
        return config.defaultTrapFocus;
      }
    },
    ariaRole: {
      type: String,
      validator: function validator6(value) {
        return ["dialog", "alertdialog"].indexOf(value) >= 0;
      }
    },
    ariaModal: Boolean
  },
  data: function data2() {
    var prompt2 = this.hasInput ? this.inputAttrs.value || "" : "";
    return {
      prompt: prompt2,
      isActive: false,
      validationMessage: "",
      isCompositing: false,
      isLoading: false
    };
  },
  computed: {
    dialogClass: function dialogClass() {
      return [this.size, {
        "has-custom-container": this.container !== null
      }];
    },
    /**
    * Icon name (MDI) based on the type.
    */
    iconByType: function iconByType() {
      switch (this.type) {
        case "is-info":
          return "information";
        case "is-success":
          return "check-circle";
        case "is-warning":
          return "alert";
        case "is-danger":
          return "alert-circle";
        default:
          return null;
      }
    },
    showCancel: function showCancel() {
      return this.cancelOptions.indexOf("button") >= 0;
    }
  },
  methods: {
    /**
    * If it's a prompt Dialog, validate the input.
    * Call the onConfirm prop (function) and close the Dialog.
    */
    confirm: function confirm() {
      var _this = this;
      if (this.$refs.input !== void 0) {
        if (this.isCompositing) return;
        if (!this.$refs.input.checkValidity()) {
          this.validationMessage = this.$refs.input.validationMessage;
          this.$nextTick(function() {
            return _this.$refs.input.select();
          });
          return;
        }
      }
      this.$emit("confirm", this.prompt);
      this.onConfirm(this.prompt, this);
      if (this.closeOnConfirm) this.close();
    },
    /**
    * Close the Dialog.
    */
    close: function close2() {
      var _this2 = this;
      this.isActive = false;
      this.isLoading = false;
      setTimeout(function() {
        _this2.$destroy();
        removeElement(_this2.$el);
      }, 150);
    },
    /**
    * Start the Loading.
    */
    startLoading: function startLoading() {
      this.isLoading = true;
    },
    /**
    * Cancel the Loading.
    */
    cancelLoading: function cancelLoading() {
      this.isLoading = false;
    }
  },
  beforeMount: function beforeMount2() {
    var _this3 = this;
    if (typeof window !== "undefined") {
      this.$nextTick(function() {
        var container = document.querySelector(_this3.container) || document.body;
        container.appendChild(_this3.$el);
      });
    }
  },
  mounted: function mounted2() {
    var _this4 = this;
    this.isActive = true;
    if (typeof this.inputAttrs.required === "undefined") {
      this.$set(this.inputAttrs, "required", true);
    }
    this.$nextTick(function() {
      if (_this4.hasInput) {
        _this4.$refs.input.focus();
      } else if (_this4.focusOn === "cancel" && _this4.showCancel) {
        _this4.$refs.cancelButton.$el.focus();
      } else {
        _this4.$refs.confirmButton.$el.focus();
      }
    });
  }
};
var __vue_script__4 = script4;
var __vue_render__4 = function() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("transition", { attrs: { "name": _vm.animation } }, [_vm.isActive ? _c("div", { directives: [{ name: "trap-focus", rawName: "v-trap-focus", value: _vm.trapFocus, expression: "trapFocus" }], staticClass: "dialog modal is-active", class: _vm.dialogClass, attrs: { "role": _vm.ariaRole, "aria-modal": _vm.ariaModal } }, [_c("div", { staticClass: "modal-background", on: { "click": function($event) {
    return _vm.cancel("outside");
  } } }), _c("div", { staticClass: "modal-card animation-content" }, [_vm.title ? _c("header", { staticClass: "modal-card-head" }, [_c("p", { staticClass: "modal-card-title" }, [_vm._v(_vm._s(_vm.title))])]) : _vm._e(), _c("section", { staticClass: "modal-card-body", class: { "is-titleless": !_vm.title, "is-flex": _vm.hasIcon } }, [_c("div", { staticClass: "media" }, [_vm.hasIcon && (_vm.icon || _vm.iconByType) ? _c("div", { staticClass: "media-left" }, [_c("b-icon", { attrs: { "icon": _vm.icon ? _vm.icon : _vm.iconByType, "pack": _vm.iconPack, "type": _vm.type, "both": !_vm.icon, "size": "is-large" } })], 1) : _vm._e(), _c("div", { staticClass: "media-content" }, [_c("p", [_vm.$slots.default ? [_vm._t("default")] : [_c("div", { domProps: { "innerHTML": _vm._s(_vm.message) } })]], 2), _vm.hasInput ? _c("div", { staticClass: "field" }, [_c("div", { staticClass: "control" }, [_vm.inputAttrs.type === "checkbox" ? _c("input", _vm._b({ directives: [{ name: "model", rawName: "v-model", value: _vm.prompt, expression: "prompt" }], ref: "input", staticClass: "input", class: { "is-danger": _vm.validationMessage }, attrs: { "type": "checkbox" }, domProps: { "checked": Array.isArray(_vm.prompt) ? _vm._i(_vm.prompt, null) > -1 : _vm.prompt }, on: { "compositionstart": function($event) {
    _vm.isCompositing = true;
  }, "compositionend": function($event) {
    _vm.isCompositing = false;
  }, "keydown": function($event) {
    if (!$event.type.indexOf("key") && _vm._k($event.keyCode, "enter", 13, $event.key, "Enter")) {
      return null;
    }
    return _vm.confirm($event);
  }, "change": function($event) {
    var $$a = _vm.prompt, $$el = $event.target, $$c = $$el.checked ? true : false;
    if (Array.isArray($$a)) {
      var $$v = null, $$i = _vm._i($$a, $$v);
      if ($$el.checked) {
        $$i < 0 && (_vm.prompt = $$a.concat([$$v]));
      } else {
        $$i > -1 && (_vm.prompt = $$a.slice(0, $$i).concat($$a.slice($$i + 1)));
      }
    } else {
      _vm.prompt = $$c;
    }
  } } }, "input", _vm.inputAttrs, false)) : _vm.inputAttrs.type === "radio" ? _c("input", _vm._b({ directives: [{ name: "model", rawName: "v-model", value: _vm.prompt, expression: "prompt" }], ref: "input", staticClass: "input", class: { "is-danger": _vm.validationMessage }, attrs: { "type": "radio" }, domProps: { "checked": _vm._q(_vm.prompt, null) }, on: { "compositionstart": function($event) {
    _vm.isCompositing = true;
  }, "compositionend": function($event) {
    _vm.isCompositing = false;
  }, "keydown": function($event) {
    if (!$event.type.indexOf("key") && _vm._k($event.keyCode, "enter", 13, $event.key, "Enter")) {
      return null;
    }
    return _vm.confirm($event);
  }, "change": function($event) {
    _vm.prompt = null;
  } } }, "input", _vm.inputAttrs, false)) : _c("input", _vm._b({ directives: [{ name: "model", rawName: "v-model", value: _vm.prompt, expression: "prompt" }], ref: "input", staticClass: "input", class: { "is-danger": _vm.validationMessage }, attrs: { "type": _vm.inputAttrs.type }, domProps: { "value": _vm.prompt }, on: { "compositionstart": function($event) {
    _vm.isCompositing = true;
  }, "compositionend": function($event) {
    _vm.isCompositing = false;
  }, "keydown": function($event) {
    if (!$event.type.indexOf("key") && _vm._k($event.keyCode, "enter", 13, $event.key, "Enter")) {
      return null;
    }
    return _vm.confirm($event);
  }, "input": function($event) {
    if ($event.target.composing) {
      return;
    }
    _vm.prompt = $event.target.value;
  } } }, "input", _vm.inputAttrs, false))]), _c("p", { staticClass: "help is-danger" }, [_vm._v(_vm._s(_vm.validationMessage))])]) : _vm._e()])])]), _c("footer", { staticClass: "modal-card-foot" }, [_vm.showCancel ? _c("b-button", { ref: "cancelButton", attrs: { "disabled": _vm.isLoading }, on: { "click": function($event) {
    return _vm.cancel("button");
  } } }, [_vm._v(_vm._s(_vm.cancelText))]) : _vm._e(), _c("b-button", { ref: "confirmButton", attrs: { "type": _vm.type, "loading": _vm.isLoading }, on: { "click": _vm.confirm } }, [_vm._v(_vm._s(_vm.confirmText))])], 1)])]) : _vm._e()]);
};
var __vue_staticRenderFns__4 = [];
var __vue_inject_styles__4 = void 0;
var __vue_scope_id__4 = void 0;
var __vue_module_identifier__4 = void 0;
var __vue_is_functional_template__4 = false;
var __vue_component__4 = normalizeComponent(
  { render: __vue_render__4, staticRenderFns: __vue_staticRenderFns__4 },
  __vue_inject_styles__4,
  __vue_script__4,
  __vue_scope_id__4,
  __vue_is_functional_template__4,
  __vue_module_identifier__4,
  false,
  void 0,
  void 0,
  void 0
);
var Dialog = __vue_component__4;
var localVueInstance;
function open(propsData) {
  var slot;
  if (Array.isArray(propsData.message)) {
    slot = propsData.message;
    delete propsData.message;
  }
  var vm = typeof window !== "undefined" && window.Vue ? window.Vue : localVueInstance || VueInstance;
  var DialogComponent = vm.extend(Dialog);
  var component = new DialogComponent({
    el: document.createElement("div"),
    propsData
  });
  if (slot) {
    component.$slots.default = slot;
    component.$forceUpdate();
  }
  if (!config.defaultProgrammaticPromise) {
    return component;
  } else {
    return new Promise(function(resolve) {
      component.$on("confirm", function(event) {
        return resolve({
          result: event || true,
          dialog: component
        });
      });
      component.$on("cancel", function() {
        return resolve({
          result: false,
          dialog: component
        });
      });
    });
  }
}
var DialogProgrammatic = {
  alert: function alert(params) {
    if (typeof params === "string") {
      params = {
        message: params
      };
    }
    var defaultParam = {
      canCancel: false
    };
    var propsData = merge(defaultParam, params);
    return open(propsData);
  },
  confirm: function confirm2(params) {
    var defaultParam = {};
    var propsData = merge(defaultParam, params);
    return open(propsData);
  },
  prompt: function prompt(params) {
    var defaultParam = {
      hasInput: true
    };
    var propsData = merge(defaultParam, params);
    return open(propsData);
  }
};
var Plugin = {
  install: function install(Vue) {
    localVueInstance = Vue;
    registerComponent(Vue, Dialog);
    registerComponentProgrammatic(Vue, "dialog", DialogProgrammatic);
  }
};
use(Plugin);

export {
  normalizeComponent,
  use,
  registerComponent,
  registerComponentProgrammatic,
  Icon,
  Button,
  trapFocus,
  Modal,
  Dialog,
  DialogProgrammatic,
  Plugin
};
//# sourceMappingURL=chunk-AZE24IOE.js.map
