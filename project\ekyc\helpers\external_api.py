import requests
import json
from pydash import get
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache

APP_ID = settings.EKYC_APP_ID
APP_SECRET = settings.EKYC_APP_SECRET
ENDPOINT = settings.EKYC_ENDPOINT

LIVENESS_VERSION = settings.EKYC_ENDPOINT_LIVENESS_VERSION
OCR_VERSION = settings.EKYC_ENDPOINT_OCR_VERSION
FACE_COMPARE_VERSION = settings.EKYC_ENDPOINT_FACE_COMPARE_VERSION
BACK_CARD_VERSION = settings.EKYC_ENDPOINT_BACK_CARD_VERSION

CACHE_TIMEOUT = settings.EKYC_CACHE_TIMEOUT
UPLOAD_EXPIRE_TIME = settings.EKYC_UPLOAD_EXPIRE_SECONDS

TOKEN_CACHE_TIMEOUT = 60
CACHE_KEY = "ekyc_token"
FILE_NAME = "media.jpg"
FILE_MIME_TYPE = "image/jpeg"
BEARER_TOKEN_PREFIX = "Bearer "


def getToken(process_logs=None):
    if process_logs is None:
        process_logs = []

    token = cache.get(CACHE_KEY)
    if token is not None:
        return token

    headers = {
        "app_secret": APP_SECRET,
        "app_id": APP_ID,
    }

    process_logs.append(["get_token_api_gateway", str(datetime.now().isoformat())])
    r = requests.post(ENDPOINT + "/login", headers=headers)
    if r.status_code == 200:
        token = r.text
        cache.set(CACHE_KEY, token, TOKEN_CACHE_TIMEOUT)
        return token
    return None


def get_current_time():
    return datetime.now()


def get_expire_at():
    current_time = get_current_time()
    expire_at = current_time + timedelta(seconds=UPLOAD_EXPIRE_TIME)
    return expire_at.isoformat()


def upload_liveness(
    files=[], transaction_id="", logs=[], api_version=None, additional_header={}, additional_body={}, process_logs=None
):
    if process_logs is None:
        process_logs = []

    if api_version is None:
        api_version = LIVENESS_VERSION
    headers = {
        "Authorization": BEARER_TOKEN_PREFIX + getToken(process_logs),
        "app_id": APP_ID,
        "transaction_id": transaction_id,
        **additional_header,
    }
    body = {"expire_at": get_expire_at(), "liveness_logs": json.dumps(logs), **additional_body}

    file_list = [("images", (file.name, file, FILE_MIME_TYPE)) for file in files]

    process_logs.append(["upload_api_gateway", str(datetime.now().isoformat())])
    r = requests.post(
        ENDPOINT + api_version + "/liveness",
        data=body,
        files=file_list,
        headers=headers,
    )

    try:
        upload_response = r.json()
    except Exception:
        upload_response = r.text

    if r.status_code == 200:
        final_response = {
            "code": r.status_code,
            "status": "success",
            "data": upload_response["request_id"],
            "result": upload_response["result"],
            "face_compare": upload_response["face_compare"],
            "api_version": api_version,
            "additional_header": additional_header,
            "additional_body": additional_body,
        }
    else:
        final_response = {
            "code": r.status_code,
            "status": "fail",
            "error_data": upload_response,
            "api_version": api_version,
            "additional_header": additional_header,
            "additional_body": additional_body,
        }

    return final_response, r.status_code


# UPLOAD DOCUMENTS
def _build_upload_document_function(document_type, api_document_path):
    def _upload_document_function(
        file={},
        transaction_id="",
        api_version=None,
        additional_header={},
        additional_body={},
        process_logs=None,
        logs=None,
    ):
        resolved_document_type = document_type

        if process_logs is None:
            process_logs = []

        if api_version is None:
            api_version = OCR_VERSION

        headers = {
            "Authorization": BEARER_TOKEN_PREFIX + getToken(process_logs),
            "app_id": APP_ID,
            "transaction_id": transaction_id,
            **additional_header,
        }
        body = {"expire_at": get_expire_at(), **additional_body}
        file_image = [("image", (FILE_NAME, file, FILE_MIME_TYPE))]

        process_logs.append(["upload_api_gateway", str(datetime.now().isoformat())])
        r = requests.post(
            ENDPOINT + api_version + api_document_path,
            data=body,
            files=file_image,
            headers=headers,
        )

        try:
            upload_response = r.json()
        except Exception:
            upload_response = r.text

        if r.status_code == 200:
            gateway_document_type = get(upload_response, ["document_type"], {})
            if gateway_document_type:
                resolved_document_type = gateway_document_type

            final_response = {
                "code": r.status_code,
                "status": "success",
                "data": upload_response,
                "ocr": get(upload_response, ["gateway_result", "document", "extracted"], False),
                "warning": get(upload_response, ["gateway_result", "warning"], {}),
                "document_type": resolved_document_type,
                "api_version": api_version,
                "api_document_path": api_document_path,
                "additional_header": additional_header,
                "additional_body": additional_body,
                "logs": logs,
            }

            # If ocr is not included, request result
            if not get(final_response, "ocr") and get(upload_response, ["result", "ocr", "status"]) == True:
                process_logs.append(["get_result_api_gateway", str(datetime.now().isoformat())])
                get_result_method_to_call = globals()[f"get_result_{resolved_document_type}"]
                ocr_response, ocr_status = get_result_method_to_call(upload_response["request_id"])
                if ocr_status == 200:
                    final_response["ocr"] = ocr_response["result"]["document"]["extracted"]
                    final_response["warning"] = ocr_response["result"].get("warning", {})
                else:
                    final_response["ocr"] = ocr_response

        else:
            final_response = {
                "code": r.status_code,
                "status": "fail",
                "error_data": upload_response,
                "document_type": document_type,
                "api_version": api_version,
                "additional_header": additional_header,
                "additional_body": additional_body,
                "logs": logs,
            }

        return final_response, r.status_code

    return _upload_document_function


def upload_back_card(
    file={},
    transaction_id="",
    api_version=None,
    additional_header={},
    additional_body={},
    process_logs=None,
    logs=None,
):
    if process_logs is None:
        process_logs = []

    if api_version is None:
        api_version = BACK_CARD_VERSION

    headers = {
        "Authorization": BEARER_TOKEN_PREFIX + getToken(process_logs),
        "app_id": APP_ID,
        "transaction_id": transaction_id,
        **additional_header,
    }
    body = {**additional_body}
    file_image = [("image", (FILE_NAME, file, FILE_MIME_TYPE))]

    process_logs.append(["upload_api_gateway", str(datetime.now().isoformat())])
    r = requests.post(ENDPOINT + api_version + "/back", data=body, files=file_image, headers=headers)

    try:
        upload_response = r.json()
    except Exception:
        upload_response = r.text

    if r.status_code == 200:
        final_response = {
            "code": r.status_code,
            "status": "success",
            "data": upload_response,
            "ocr": get(upload_response, ["gateway_result", "document", "extracted"], False),
            "warning": get(upload_response, ["gateway_result", "warning"], {}),
            "api_version": api_version,
            "additional_header": additional_header,
            "additional_body": additional_body,
            "logs": logs,
        }

        # If ocr is not included, request result
        if not get(final_response, "ocr") and get(upload_response, ["result", "ocr", "status"]) == True:
            process_logs.append(["get_result_api_gateway", str(datetime.now().isoformat())])
            ocr_response, ocr_status = get_result_back_card(upload_response["request_id"])
            if ocr_status == 200:
                final_response["ocr"] = ocr_response["result"]["document"]["extracted"]
                final_response["warning"] = ocr_response["result"].get("warning", {})
            else:
                final_response["ocr"] = ocr_response
    else:
        final_response = {
            "code": r.status_code,
            "status": "fail",
            "error_data": upload_response,
            "api_version": api_version,
            "additional_header": additional_header,
            "additional_body": additional_body,
            "logs": logs,
        }

    return final_response, r.status_code


def compare(transaction_id, liveness_id, cloud_vision_id, api_version=None, additional_header={}):
    if api_version is None:
        api_version = FACE_COMPARE_VERSION
    headers = {
        "Authorization": BEARER_TOKEN_PREFIX + getToken(),
        "app_id": APP_ID,
        "transaction_id": transaction_id,
        **additional_header,
    }
    body = {"request_id1": liveness_id, "request_id2": cloud_vision_id}
    r = requests.post(ENDPOINT + api_version + "/face_compare", data=body, headers=headers)
    return r.text, r.status_code


def get_result_liveness(request_id, *args, **kwargs):
    key = f"{CACHE_KEY}_liveness_{request_id}"
    r = cache.get(key)
    if r is not None:
        return r, 200

    headers = {
        "Authorization": BEARER_TOKEN_PREFIX + getToken(),
        "app_id": APP_ID,
        "request_id": request_id,
    }
    body = {}
    r = requests.get(ENDPOINT + "/liveness/result", data=body, headers=headers)
    try:
        res = r.json()
    except:
        res = r.text
    if r.status_code == 200:
        cache.set(key, res, CACHE_TIMEOUT)
    return res, r.status_code


# GET RESULT OF DOCUMENTS
def _build_get_result_document_function(document_type, api_path):
    def _get_result_document_function(request_id, *args, **kwargs):
        key = f"{CACHE_KEY}_{document_type}_{request_id}"
        r = cache.get(key)
        if r is not None:
            return r, 200

        headers = {
            "Authorization": BEARER_TOKEN_PREFIX + getToken(),
            "app_id": APP_ID,
            "request_id": request_id,
        }
        body = {}
        r = requests.get(ENDPOINT + api_path, data=body, headers=headers)
        try:
            res = r.json()
        except:
            res = r.text
        if r.status_code == 200:
            cache.set(key, res, CACHE_TIMEOUT)
        return res, r.status_code

    return _get_result_document_function


def get_result_back_card(request_id, *args, **kwargs):
    key = f"{CACHE_KEY}_backcard_{request_id}"
    r = cache.get(key)
    if r is not None:
        return r, 200

    headers = {
        "Authorization": BEARER_TOKEN_PREFIX + getToken(),
        "app_id": APP_ID,
        "request_id": request_id,
    }
    body = {}
    r = requests.get(ENDPOINT + "/back/result", data=body, headers=headers)
    try:
        res = r.json()
    except:
        res = r.text
    if r.status_code == 200:
        cache.set(key, res, CACHE_TIMEOUT)
    return res, r.status_code


def get_result_face_compare(transaction_id, request_id, *args, **kwargs):
    key = f"{CACHE_KEY}_facecompare_{request_id}"
    r = cache.get(key)
    if r is not None:
        return r, 200

    headers = {
        "Authorization": BEARER_TOKEN_PREFIX + getToken(),
        "app_id": APP_ID,
        "transaction_id": transaction_id,
        "request_id": request_id,
    }
    body = {}
    r = requests.get(ENDPOINT + "/face_compare/result", data=body, headers=headers)
    try:
        res = r.json()
    except:
        res = r.text
    if r.status_code == 200:
        cache.set(key, res, CACHE_TIMEOUT)
    return res, r.status_code


def get_result_by_type(field_type, request_id=None, *args, **kwargs):
    return globals().get(f"get_result_{field_type}")(request_id=request_id)


# CHECK IMAGE QUALITY OF DOCUMENTS
def _build_check_quality_function(check_quality_path):
    def _check_quality_function(file={}, transaction_id="", api_version=None, additional_header={}, *args, **kwargs):
        if api_version is None:
            api_version = "/v1"
        headers = {
            "Authorization": BEARER_TOKEN_PREFIX + getToken(),
            "app_id": APP_ID,
            "transaction_id": transaction_id,
            **additional_header,
        }
        body = {}
        file_image = [("image", (FILE_NAME, file, FILE_MIME_TYPE))]

        r = requests.post(
            ENDPOINT + api_version + check_quality_path,
            data=body,
            files=file_image,
            headers=headers,
        )
        try:
            return r.json(), r.status_code
        except Exception:
            return r.text, r.status_code

    return _check_quality_function


upload_front_card = _build_upload_document_function("front_card", "/ocr/national_id_card")
upload_passport = _build_upload_document_function("passport", "/ocr/passport")
upload_driver_license = _build_upload_document_function("driver_license", "/ocr/driver_license")
upload_residence_permit = _build_upload_document_function("residence_permit", "/ocr/residence_permit")
upload_thai_alien_card = _build_upload_document_function("thai_alien_card", "/ocr/alien_card")
upload_portrait = _build_upload_document_function("portrait", "/ocr/portrait")
upload_ci_passport = _build_upload_document_function("ci_passport", "/ocr/ci_passport")
upload_work_permit_card = _build_upload_document_function("work_permit_card", "/ocr/work_permit_card")
upload_work_permit_book = _build_upload_document_function("work_permit_book", "/ocr/work_permit_book")
upload_travel_document = _build_upload_document_function("travel_document", "/ocr/travel_document")
upload_white_card = _build_upload_document_function("white_card", "/ocr/white_card")
upload_border_pass = _build_upload_document_function("border_pass", "/ocr/border_pass")
upload_monk_card = _build_upload_document_function("monk_card", "/ocr/monk_card")
upload_immigration_card = _build_upload_document_function("immigration_card", "/ocr/immigration_card")
upload_other_document = _build_upload_document_function("other_document", "/ocr/other_document")
upload_auto = _build_upload_document_function("auto", "/ocr/auto")

get_result_front_card = _build_get_result_document_function("front_card", "/ocr/result")
get_result_passport = _build_get_result_document_function("passport", "/ocr/result")
get_result_driver_license = _build_get_result_document_function("driver_license", "/ocr/result")
get_result_residence_permit = _build_get_result_document_function("residence_permit", "/ocr/result")
get_result_thai_alien_card = _build_get_result_document_function("thai_alien_card", "/ocr/result")
get_result_portrait = _build_get_result_document_function("portrait", "/ocr/result")
get_result_ci_passport = _build_get_result_document_function("ci_passport", "/ocr/result")
get_result_work_permit_card = _build_get_result_document_function("work_permit_card", "/ocr/result")
get_result_work_permit_book = _build_get_result_document_function("work_permit_book", "/ocr/result")
get_result_travel_document = _build_get_result_document_function("travel_document", "/ocr/result")
get_result_white_card = _build_get_result_document_function("white_card", "/ocr/result")
get_result_border_pass = _build_get_result_document_function("border_pass", "/ocr/result")
get_result_monk_card = _build_get_result_document_function("monk_card", "/ocr/result")
get_result_immigration_card = _build_get_result_document_function("immigration_card", "/ocr/result")
get_result_other_document = _build_get_result_document_function("other_document", "/ocr/result")
get_result_auto = _build_get_result_document_function("auto", "/ocr/result")
get_result_quality = get_result_front_card


check_quality_front_card = _build_check_quality_function("/quality/national_id_card")


EKYC_UPLOADER_MAP = {
    "front_card": upload_front_card,
    "passport": upload_passport,
    "driver_license": upload_driver_license,
    "residence_permit": upload_residence_permit,
    "thai_alien_card": upload_thai_alien_card,
    "portrait": upload_portrait,
    "ci_passport": upload_ci_passport,
    "work_permit_card": upload_work_permit_card,
    "work_permit_book": upload_work_permit_book,
    "travel_document": upload_travel_document,
    "white_card": upload_white_card,
    "border_pass": upload_border_pass,
    "monk_card": upload_monk_card,
    "immigration_card": upload_immigration_card,
    "other_document": upload_other_document,
    "auto": upload_auto,
}
