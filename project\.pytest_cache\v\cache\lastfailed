{"appsetting/tests/test_api.py::SettingsTests::test_get_list_for_admin": true, "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validate_document": true, "dynamicform/tests/submodules/form/functions/test_assign_user.py::AssignUserTest::test_auto_assign_user_after_create_form": true, "ekyc/tests/test_form.py::EkycTests::test_get_report_preview_url": true, "ekyc/tests/test_form_front.py::EkycTests::test_passport_mrz": true, "ekyc/tests/test_form_passport.py::EkycTests::test_passport": true, "ekyc/tests/test_preview_url.py::EkycTests::test_get_report_preview_url": true, "ekyc/tests/test_result.py::EkycResultTests::test_result_flow": true, "ndid/tests/test_api.py::NdidTests::test_failed_flow": true, "ndid/tests/test_api.py::NdidTests::test_get_idps": true, "ndid/tests/test_api.py::NdidTests::test_success_flow": true, "workspace/tests/test_checkout.py::TestCheckOutTestCase::test_checkout_can_create_transaction": true, "workspace/tests/test_checkout.py::TestCheckOutTestCase::test_checkout_response_error": true, "workspace/tests/test_checkout_auto_renew_callback.py::TestCheckOutTestCase::test_auto_renew_callback_charge_success": true, "workspace/tests/test_checkout_auto_renew_callback.py::TestCheckOutTestCase::test_auto_renew_callback_payment_intent_success": true, "workspace/tests/test_checkout_auto_renew_callback.py::TestCheckOutTestCase::test_auto_renew_payment": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_auto_renew_trigger": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_1_credit": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_3_credit": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_credit_for_calculate_alert_email": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_expired_flow_2_over_package": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_flow": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_flow_2_over_package": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_flow_over_1_package": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_no_credit": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_send_email": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_charge_success": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_payment_intent_created": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_payment_intent_success": true, "workspace/tests/test_credit_usage_history.py::TestUpdateCreditUsage::test_auto_renew_callback_charge_success": true, "ekyc/tests/test_ocr_extractor.py::EkycTests::test_fullname_but_no_firstname_lastname": true, "decision_flow/tests/compiler/test_compile.py::CompileTest::test_compile_simple": true, "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_NOT_FOUND-test_case_input11-expected_result11]": true, "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_CORRUPTION_FOUND-test_case_input14-expected_result14]": true, "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_CORRUPTION_FOUND-test_case_input15-expected_result15]": true, "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_CORRUPTION_FOUND-test_case_input11-expected_result11]": true, "smartuploader/tests/test_callback.py::CallbackTest::test_callback_no_editor_permission": true, "smartuploader/tests/test_callback.py::CallbackTest::test_callback_not_in_workspace": true, "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_called": true, "smartuploader/tests/test_model.py::ModelTest::test_info_complete": true, "smartuploader/tests/test_model.py::ModelTest::test_info_passed": true, "ekyc/tests/test_back.py::BackCardTests::test_api_report": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_call_charge_success_then_checkout_session_completed": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_call_checkout_session_completed_then_charge_success": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_get_payment_intent_id_from_event": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_ignore_update_transaction_status": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_paid_package": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_charge_fail": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_charge_update": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_checkout_session_completed": true, "workspace/tests/test_permissions.py::PermissionTests::test_api_create_edit_duplicate_flow": true, "ekyc/tests/test_form_passport.py::EkycTests::test_passport_nfc": true, "ekyc/tests/test_form_passport.py::EkycTests::test_passport_nfc_pass": true, "ekyc/tests/test_nfc.py::EkycTests::test_passport_nfc_none": true, "ekyc/tests/test_nfc.py::EkycTests::test_passport_nfc_pass": true, "ekyc/tests/test_form_nfc.py::EkycTests::test_passport_nfc_pass": true, "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_fail": true, "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_no_document": true, "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_none": true, "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_pass": true, "ekyc/tests/test_form_nfc.py::EkycTests::test_other_document_should_not_check_nfc": true, "workspace/tests/test_permissions.py::PermissionTests::test_api_view_ekyc_report_result": true, "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_can_create_with_base64_portrait": true, "dynamicform/tests/test_face_compare_integration.py::FaceCompareIntegrationTests::test_update_face_compare_message": true, "ekyc/tests/test_ocr_extractor.py::EkycTests::test_alien_should_not_expiry": true, "ekyc/tests/test_checker_expiry.py::test_expiry_condition_excluded_document_types": true, "ekyc/tests/test_checker_expiry.py::test_expiry_check_valid_document": true, "ekyc/tests/test_checker_expiry.py::test_expiry_check_expired_document": true, "ekyc/tests/test_checker_expiry.py::test_expiry_check_lifelong_document": true, "ekyc/tests/test_checker_expiry.py::test_expiry_not_found": true, "ekyc/tests/test_checker_expiry.py::test_expiry_not_found_ignored": true, "ekyc/tests/test_checker_expiry.py::test_expiry_check_disabled": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_document_upload_without_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyLivenessTests::test_liveness_upload_includes_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyLivenessTests::test_liveness_upload_without_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_liveness_upload_includes_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_liveness_upload_without_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_document_upload_includes_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::GetAdditionalHeaderTests::test_get_additional_header_includes_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::GetAdditionalHeaderTests::test_get_additional_header_with_x_forwarded_for": true, "ekyc/tests/test_blacklist_key.py::GetAdditionalHeaderTests::test_get_additional_header_without_blacklist_key": true, "ekyc/tests/test_api_gateway_header_body.py::TestBaseDocument::test_submit_additional_header_settings": true, "app/auth/tests/test_logout_jwt.py::TestLogoutJWT::test_logout_jwt_cannot_reuse_token": true, "app/auth/tests/test_set_password.py::TestSetPassword::test_can_change_password": true, "app/auth/tests/test_set_password.py::TestSetPassword::test_lock_change_password": true, "app/auth/tests/test_set_password.py::TestSetPassword::test_lock_duration_flow": true, "app/tests/test_custom_http_header.py::TestVerifyQueryDict::test_return_custom_http_headers": true, "app/tests/test_user_encode_token.py::TestUserEncodeToken::test_encode_with_exp": true, "app/tests/test_user_encode_token.py::TestUserEncodeToken::test_encode_without_expired": true, "app/tests/test_user_serializer.py::TestUserDetailsSerializer::test_none_super_user": true, "app/tests/test_user_serializer.py::TestUserDetailsSerializer::test_super_user": true, "appsetting/tests/test_api.py::SettingsTests::test_delete": true, "appsetting/tests/test_api.py::SettingsTests::test_get_info": true, "appsetting/tests/test_api.py::SettingsTests::test_get_input_form": true, "appsetting/tests/test_api.py::SettingsTests::test_get_list": true, "appsetting/tests/test_api.py::SettingsTests::test_get_list_for_edit": true, "appsetting/tests/test_api.py::SettingsTests::test_post_create": true, "appsetting/tests/test_api.py::SettingsTests::test_post_update": true, "appsetting/tests/test_api.py::SettingsTests::test_post_update_batch": true, "appsetting/tests/test_api.py::SettingsTests::test_run_possible_command_only": true, "appsetting/tests/test_command.py::SettingsTests::test_initsettings": true, "appsetting/tests/test_command.py::SettingsTests::test_readenvsettings": true, "appsetting/tests/test_inject.py::SettingsTests::test_injection_header": true, "appsetting/tests/test_inject.py::SettingsTests::test_overridden_value": true, "bankstatement/tests/test_api_v2.py::BankstatementV2Tests::test_api_result": true, "bankstatement/tests/test_api_v2.py::BankstatementV2Tests::test_api_upload": true, "bankstatement/tests/test_api_v2.py::BankstatementV2Tests::test_create_batch_no_duplicate": true, "bankstatement/tests/test_api_v2.py::BankstatementV2Tests::test_flag_task": true, "bankstatement/tests/test_support_bank_list.py::BankStatementSupportBankListTests::test_get_support_list_default": true, "bankstatement/tests/test_support_bank_list.py::BankStatementSupportBankListTests::test_get_support_list_with_invalid_lang_params": true, "bankstatement/tests/test_support_bank_list.py::BankStatementSupportBankListTests::test_get_support_list_with_params": true, "bankstatement/tests/test_validate_bank_statement.py::BankStatementDocumentTests::test_validation_empty_documents": true, "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_only_first_name_match": true, "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validation_name_match": true, "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validation_producer_check": true, "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validation_same_creation_mod_date": true, "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validation_valid_account_number": true, "data_point/tests/comply_advantage/test_comply_advantage.py::TestComplyAdvantage::test_comply_advantage": true, "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_call_DBD_and_Creden": true, "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_call_DBD_and_Creden_and_all_shareholder_case_1_and_3": true, "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_call_DBD_and_Creden_and_all_shareholder_case_2_and_3": true, "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_call_DBD_and_Creden_and_some_shareholder_from_input_name_case_1_and_2": true, "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_list_of_full_name_is_None_case_1_and_3": true, "data_point/tests/provider/test_answer_2.py::TestAnswerWithDB::test_get_form_local_set": true, "data_point/tests/provider/test_answer_2.py::TestAnswerWithDB::test_get_item_label": true, "data_point/tests/provider/test_answer_2.py::TestAnswerWithDB::test_set_form_item_labels": true, "data_point/tests/utils/test_prefix_removal.py::TestPrefixRemoval::test_clean_in_prefix_removal": true, "decision_flow/tests/compiler/test_compile.py::CompileTest::test_compile_simple_completed": true, "decision_flow/tests/compiler/test_compile.py::CompileTest::test_compile_simple_fail": true, "decision_flow/tests/compiler/test_error.py::CompileErrorTest::test_condition_error": true, "dynamicform/tests/schema/lang/test_form_locale.py::FormLocaleTest::test_can_translate_frontend_schema": true, "dynamicform/tests/submodules/answer/test_log.py::SaveTest::test_can_save_and_log_update": true, "dynamicform/tests/submodules/answer/test_log.py::SaveTest::test_can_save_not_log": true, "dynamicform/tests/submodules/answer/test_log.py::SaveTest::test_can_save_with_answers": true, "dynamicform/tests/submodules/answerfile/test_delete.py::DeleteTest::test_can_delete_file": true, "dynamicform/tests/submodules/answerfile/test_delete.py::DeleteTest::test_cannot_delete_after_submit_form": true, "dynamicform/tests/submodules/answerfile/test_preview.py::PreviewTest::test_can_preview_file": true, "dynamicform/tests/submodules/answerfile/test_upload_file.py::SaveTest::test_can_upload_file_save_multiple_false": true, "dynamicform/tests/submodules/answerfile/test_upload_file.py::SaveTest::test_can_upload_multiple_files": true, "dynamicform/tests/submodules/answerfile/test_upload_file.py::SaveTest::test_can_upload_one_file": true, "dynamicform/tests/submodules/answerfile/test_upload_file.py::SaveTest::test_cannot_update_after_submit_form": true, "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_can_applied_form": true, "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_can_applied_form_with_answers": true, "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_can_applied_form_with_miss_required_answers": true, "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_can_applied_form_with_recaptcha": true, "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_cannot_applied_form_with_fail_answers": true, "dynamicform/tests/submodules/appliedform/test_get_answer.py::SaveGetAnswer::test_can_get_answers": true, "dynamicform/tests/submodules/appliedform/test_info.py::InfoTest::test_can_view_info": true, "dynamicform/tests/submodules/appliedform/test_info.py::InfoTest::test_multi_info": true, "dynamicform/tests/submodules/appliedform/test_optimize.py::OptimizeTest::test_can_save_with_answers": true, "dynamicform/tests/submodules/appliedform/test_save.py::SaveTest::test_can_save_with_answers": true, "dynamicform/tests/submodules/appliedform/test_save.py::SaveTest::test_can_save_with_answers_validation": true, "dynamicform/tests/submodules/appliedform/test_save.py::SaveTest::test_cannot_overide_disable_answers": true, "dynamicform/tests/submodules/appliedform/test_save.py::SaveTest::test_cannot_save_disable_answers": true, "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Thailand, Bangkok case-test_case_input0-expected_result0]": true, "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Thailand, other case-test_case_input1-expected_result1]": true, "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Other country case-test_case_input2-expected_result2]": true, "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Multiple address case-test_case_input3-expected_result3]": true, "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[No country case-test_case_input4-expected_result4]": true, "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Already has full address case-test_case_input5-expected_result5]": true, "dynamicform/tests/submodules/appliedform/test_save_app_level.py::SaveTest::test_can_save_with_answers": true, "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_can_create_and_save_encrypt_answers": true, "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_can_save_encrypt_answers": true, "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_can_save_encrypt_partition_answers": true, "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_can_save_with_answers_validation": true, "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_cannot_overide_disable_answers": true, "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_cannot_save_disable_answers": true, "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_encrypt_and_decrypt_for_report": true, "dynamicform/tests/submodules/appliedform/test_save_with_file.py::SaveTest::test_can_save_with_answers": true, "dynamicform/tests/submodules/appliedform/test_save_with_file.py::SaveTest::test_cannot_submit_with_answers_validation": true, "dynamicform/tests/submodules/appliedform/test_smart_uploader_api.py::SmartUploaderAPITests::test_get_get_smartuploader_result_api": true, "dynamicform/tests/submodules/appliedform/test_smart_uploader_api.py::SmartUploaderAPITests::test_upload_file_api": true, "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_can_submit_with_answers": true, "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_can_submit_with_no_answers": true, "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_can_update_after_submit": true, "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_cannot_re_submit": true, "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_cannot_submit_with_answers_validation": true, "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_cannot_submit_with_never_answers": true, "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_cannot_update_to_submitted_form": true, "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_submit_with_consent": true, "dynamicform/tests/submodules/appliedform/test_submit_app_level.py::SubmitAppTest::test_submit_with_app_validation": true, "dynamicform/tests/submodules/appliedform/test_update_answers.py::UpdateTest::test_can_save_with_answers": true, "dynamicform/tests/submodules/decisionflow/models/test_on_other_event_failed.py::DecisionFlowOnOtherEventFailTest::test_not_trigger_on_other_event_fail": true, "dynamicform/tests/submodules/decisionflow/models/test_on_other_event_failed.py::DecisionFlowOnOtherEventFailTest::test_trigger_on_other_event_fail": true, "dynamicform/tests/submodules/decisionflow/views/test_decision_flow.py::ViewDecisionFlowTest::test_rerun_all_tasks": true, "dynamicform/tests/submodules/decisionflow/views/test_decision_flow.py::ViewDecisionFlowTest::test_rerun_selected_tasks": true, "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_can_update_if_applied_form_is_available": true, "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_cannot_update_form_inactive": true, "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_cannot_update_is_disabled": true, "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_cannot_update_is_drop_off": true, "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_cannot_update_is_submitted": true, "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_my_form_can_update": true, "dynamicform/tests/submodules/form/functions/test_change_answer_key.py::ChangeAnswerKeyTest::test_normal_case": true, "dynamicform/tests/submodules/form/mixins/test_clone.py::FormCloneTests::test_clone": true, "dynamicform/tests/submodules/form/mixins/test_clone.py::FormCloneTests::test_clone_decision_flow_schema": true, "dynamicform/tests/submodules/form/mixins/test_clone.py::FormCloneTests::test_clone_with_api": true, "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form": true, "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_and_submit_with_answers": true, "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_answers": true, "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_answers_old_version_api": true, "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_default_answers": true, "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_miss_required_answers": true, "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_recaptcha": true, "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_cannot_applied_form_and_submit_with_invalid_answers": true, "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_cannot_applied_form_with_fail_answers": true, "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_can_clone_with_ekyc": true, "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_can_clone_with_no_ekyc": true, "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_cannot_clone_form_another_workspace": true, "dynamicform/tests/submodules/form/views/test_create_form.py::CreateFormTest::test_can_create_form": true, "dynamicform/tests/submodules/form/views/test_create_form.py::CreateFormTest::test_cannot_create_form_invalid_json": true, "dynamicform/tests/submodules/form/views/test_create_form.py::CreateFormTest::test_cannot_create_form_missing_key_schema": true, "dynamicform/tests/submodules/form/views/test_create_form.py::CreateFormTest::test_cannot_create_form_with_forbidden_name": true, "dynamicform/tests/submodules/form/views/test_delete_custom_status.py::DeleteCustomStatus::test_api_can_delete_custom_status": true, "dynamicform/tests/submodules/form/views/test_delete_custom_status.py::DeleteCustomStatus::test_api_cannot_delete_custom_status": true, "dynamicform/tests/submodules/form/views/test_delete_custom_status.py::DeleteCustomStatus::test_api_cannot_delete_custom_status_from_data_point": true, "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_error_invalid_files": true, "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_error_unable_to_access_image": true, "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_error_unsupported_extension": true, "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_error_upload": true, "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_success": true, "dynamicform/tests/submodules/form/views/test_save_dashboard_settings.py::SaveDashboardSettingsTest::test_save_dashboard_settings_with_label": true, "dynamicform/tests/submodules/form/views/test_save_dashboard_settings.py::SaveDashboardSettingsTest::test_save_dashboard_settings_with_no_label": true, "dynamicform/tests/submodules/form/views/test_task.py::TaskViewTest::test_can_retrieve_task": true, "dynamicform/tests/submodules/form/views/test_update_form.py::UpdateFormTest::test_auto_build_backend_schema_after_update_form": true, "dynamicform/tests/submodules/form/views/test_update_form.py::UpdateFormTest::test_can_update_form": true, "dynamicform/tests/submodules/form/views/test_update_form.py::UpdateFormTest::test_cannot_update_form_invalid_json": true, "dynamicform/tests/submodules/form/views/test_update_form.py::UpdateFormTest::test_cannot_update_form_missing_key_schema": true, "dynamicform/tests/submodules/form/views/test_update_item.py::UpdateItemTest::test_update_smartuploader_item": true, "dynamicform/tests/submodules/form/views/test_update_report.py::UpdateFormTest::test_can_update_report": true, "dynamicform/tests/submodules/form/views/test_update_report.py::UpdateFormTest::test_cannot_update_form_invalid_json": true, "dynamicform/tests/submodules/page/test_view_permission.py::TestPageAPIPermisiionAPI::test_get_list_no_auth": true, "dynamicform/tests/submodules/page/test_view_permission.py::TestPageAPIPermisiionAPI::test_get_list_none_super_user": true, "dynamicform/tests/submodules/page/test_view_permission.py::TestPageAPIPermisiionAPI::test_get_list_super_user": true, "dynamicform/tests/submodules/speechtotext/test_convert.py::SaveTest::test_can_convert_audio": true, "ekyc/tests/test_form_front.py::EkycTests::test_front_card_check_expiry": true}