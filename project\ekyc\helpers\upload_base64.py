import base64
from django.core.files.base import ContentFile
import json
from datetime import datetime
from rest_framework import status
from pydash import get, set_

from ..helpers.log import send_log
from ..helpers.set_transaction import do_ensure_facecompare, set_document
from ..helpers.ocr_extractor import extract_document
from ..helpers.get_form import get_applied_form, get_form_document_item, get_form_answer, get_form_settings
from ..helpers.document_checker import check_by_schema_conditions
from ..helpers.document_autofill import autofill_by_schema
from ..helpers.credit import raise_if_cant_deduct_credit_document
from ..models import Ekyc

from ekyc.helpers.external_api import EKYC_UPLOADER_MAP


def get_image_part(image_base64: str):
    # Remove data:image/jpeg;base64, prefix if present
    if ";base64," in image_base64:
        image_base64 = image_base64.split(";base64,")[1]
    return image_base64


def create_document_from_base64(ekyc_ref: str, document_type: str, image_base64: str):
    try:
        # Create file from base64
        image_data = get_image_part(image_base64)
        image_file = ContentFile(base64.b64decode(image_data), name=f"{document_type}.jpg")

        # Do the submit api flow
        process_logs = [["backend_start", str(datetime.now().isoformat())]]
        applied_form = get_applied_form(ekyc_ref)

        def __send_info_log(is_fail=False):
            if applied_form:
                action = "info_backend_submit_frontcard"
                to_log = {
                    "media": "document",
                    "code": action,
                    "error": process_logs,
                    "log_type": "info",
                }
                applied_form.log(action=action, detail=process_logs, is_fail=is_fail)
                send_log(applied_form=applied_form, log=to_log)

        raise_if_cant_deduct_credit_document(applied_form)

        process_logs.append(["decrypt", str(datetime.now().isoformat())])

        nonce = None
        ensure_facecompare = None
        logs = None
        frontend_process_logs = []
        process_logs = frontend_process_logs + process_logs

        # Get ekyc
        process_logs.append(["get_ekyc", str(datetime.now().isoformat())])
        ekyc, _ = Ekyc.objects.get_or_create(ref=ekyc_ref)

        form = applied_form.form if applied_form else None
        schema = get_form_document_item(form)

        accepted_countries = get(schema, ["accepted_countries", document_type]) or ["THA"]

        api_version = get_form_settings(form, "ekyc.front_card.api_version", None)
        features = get_form_settings(form, "ekyc.front_card.features", {})

        # Get additional_header from formsettings
        additional_header_settings: dict[str, str] = get_form_settings(form, "ekyc.front_card.additional_header", {})

        # Get additional_body from formsettings
        additional_body_settings: dict[str, str] = get_form_settings(form, "ekyc.front_card.additional_body", {})

        # Get all answers_to_upload
        process_logs.append(["get_answers_to_upload", str(datetime.now().isoformat())])
        answers_to_upload_map: dict[str, str] = get_form_settings(form, "ekyc.front_card.answers_to_upload", {})
        answers_to_upload = {}
        if answers_to_upload_map and applied_form:
            for dest, src in answers_to_upload_map.items():
                answer = get_form_answer(applied_form, src)
                if not isinstance(answer, str):
                    answer = json.dumps(answer)
                answers_to_upload[dest] = answer

        additional_header = {**additional_header_settings}

        additional_body = {
            "countries": json.dumps(accepted_countries),
            "features": json.dumps(features),
            **additional_body_settings,
            **answers_to_upload,
        }

        ####################################################################
        #                           Real Request
        ####################################################################
        process_logs.append(["upload_to_api_gateway", str(datetime.now().isoformat())])
        uploader = EKYC_UPLOADER_MAP.get(document_type)
        response, upload_status = uploader(
            file=image_file,
            transaction_id=str(ekyc_ref),
            api_version=api_version,
            additional_header=additional_header,
            additional_body=additional_body,
            process_logs=process_logs,
            logs=logs,
        )

        # Extract OCR
        process_logs.append(["extract_ocr", str(datetime.now().isoformat())])
        ocr = get(response, ["ocr"], {})
        if isinstance(ocr, dict):
            added_ocr = extract_document("front_card", ocr)
        else:
            added_ocr = {}

        # Check form conditions
        process_logs.append(["check_form_conditions", str(datetime.now().isoformat())])
        pass_all_checks, _, _ = check_by_schema_conditions(
            schema=schema, response=response, with_ocr=True, ocr_result=added_ocr, applied_form=applied_form
        )

        is_success = status.is_success(upload_status) and pass_all_checks

        # OCR Autofills
        process_logs.append(["ocr_autofill", str(datetime.now().isoformat())])
        if is_success:
            autofill_by_schema(
                schema=schema,
                ocr_result=added_ocr,
                document_type=response.get("document_type"),
                applied_form=applied_form,
                response=response,
            )
        else:
            set_(response, ["autofill", "skipped"], True)

        # Save Document
        process_logs.append(["save_document", str(datetime.now().isoformat())])
        set_document(
            ekyc,
            model_name="front_card",
            response=response,
            nonce=nonce,
            passed=is_success,
            process_logs=process_logs,
        )

        ensure_facecompare_success = do_ensure_facecompare(
            ekyc, ensure_facecompare=ensure_facecompare, process_logs=process_logs
        )

        if status.is_client_error(upload_status):
            process_logs.append(["error_api_gateway_client", str(datetime.now().isoformat())])
            __send_info_log(True)
            return {"success": False, "error": f"api_gateway_client_error {upload_status}"}

        if not ensure_facecompare_success:
            process_logs.append(["error_face_compare_failed", str(datetime.now().isoformat())])
            __send_info_log(True)
            return {"success": False, "error": "face_compare_failed"}

        # Recheck again if reached attempt
        if not is_success and not ekyc.get_has_attempt("front_card"):
            process_logs.append(["error_max_attempt", str(datetime.now().isoformat())])
            __send_info_log(True)
            return {"success": False, "error": "max_attempt"}

        process_logs.append(["completed", str(datetime.now().isoformat())])
        __send_info_log(False)
        return {"success": True}
    except Exception as e:
        print(f"Error create_document_from_base64: {str(e)}")
        __send_info_log(True)
        return False
