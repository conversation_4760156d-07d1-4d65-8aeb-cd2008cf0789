{"version": 3, "sources": ["../../buefy/dist/esm/config.js"], "sourcesContent": ["import { c as config, a as setOptions } from './config-e7d4b9c2.js';\nimport { merge } from './helpers.js';\nimport './_rollupPluginBabelHelpers-df313029.js';\n\nvar ConfigComponent = {\n  getOptions: function getOptions() {\n    return config;\n  },\n  setOptions: function setOptions$1(options) {\n    setOptions(merge(config, options, true));\n  }\n};\n\nexport { ConfigComponent as default };\n"], "mappings": ";;;;;;;AAIA,IAAI,kBAAkB;AAAA,EACpB,YAAY,SAAS,aAAa;AAChC,WAAO;AAAA,EACT;AAAA,EACA,YAAY,SAAS,aAAa,SAAS;AACzC,eAAW,MAAM,QAAQ,SAAS,IAAI,CAAC;AAAA,EACzC;AACF;", "names": []}