import json
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework import status

from ekyc.tests.utils import make_payload_from_file
from ekyc.tests.utils_liveness import EkycLivenessTests
from ..models import Ekyc
from ..models.liveness import *
from .utils import KnownFaceAppMock
import os
from unittest.mock import patch, call
from ekyc.tests.mock_api import *

script_dir = os.path.dirname(__file__)


class LivenessTests(EkycLivenessTests):
    def setUp(self):
        super().setUp()
        self.client.post(reverse("ekyc:liveness-start", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}")

    def check_success_response_default_format(self, response):
        """
        {
            code: 200,
            data: "2019-10-31_11-46-40_sdztNb",
            status: "success",
            result:
            "face_compare": {
                "status": true,
                "message": "passed"
            },
            "label": {
                "status": true,
                "message": "passed"
            }
        }
        """
        self.assertEqual(response["code"], 200)
        self.assertEqual(response["status"], "success")
        self.assertIn("data", response)
        self.assertIn("result", response)
        self.assertIn("face_compare", response["result"])
        self.assertIn("label", response["result"])
        self.assertIn("status", response["result"]["face_compare"])
        self.assertIn("message", response["result"]["face_compare"])
        self.assertIn("status", response["result"]["label"])
        self.assertIn("message", response["result"]["label"])

    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_pass_all(self):
        """
        Upload face images should pass
        """
        response = self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
            self.get_liveness_passed_payload(),
        )
        self.assertTrue(status.is_success(response.status_code))
        response = json.loads(response.content)
        self.check_success_response_default_format(response)

        # Ekyc Status check
        liveness: Liveness = self.ekyc.liveness_set.first()
        self.liveness = liveness
        self.assertEqual(self.liveness.status, STATUS_SUCCESS)
        self.assertTrue(self.liveness.check_conditions())

        # Ignorance Test
        self.assertEqual(
            self.ekyc.check_liveness(schema={"configs": {"ignore": {"face_compare": False, "label": False}}}),
            True,
        )
        self.assertEqual(
            self.ekyc.check_liveness(schema={"configs": {"ignore": {"face_compare": True, "label": False}}}),
            True,
        )
        self.assertEqual(
            self.ekyc.check_liveness(schema={"configs": {"ignore": {"face_compare": False, "label": True}}}),
            True,
        )
        self.assertEqual(
            self.ekyc.check_liveness(schema={"configs": {"ignore": {"face_compare": True, "label": True}}}),
            True,
        )

    def test_api_liveness_files_not_equal_actions(self):
        """
        Unmatched file count
        """
        face_actions = json.loads(self.liveness.face_actions)
        images = self.get_liveness_passed_files()
        image_inject = SimpleUploadedFile(
            f"{face_actions[-1]}_1",
            open(os.path.join(script_dir, "../test_resources/cat.jpg"), "rb").read(),
            content_type="image/png",
        )

        files = [images[0]]
        payload = make_payload_from_file(files)

        response = self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}", payload
        )
        self.assertTrue(status.is_client_error(response.status_code))

        """
        Unsorted files
        """
        images[-1], images[-2] = images[-2], images[-1]
        payload = make_payload_from_file(images)
        response = self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}", payload
        )
        self.assertTrue(status.is_client_error(response.status_code))

    def test_api_hashing_error(self):
        """
        Injecting file in response payload should fail
        """
        face_actions = json.loads(self.liveness.face_actions)
        images = self.get_liveness_passed_files()
        image_inject = SimpleUploadedFile(
            f"{face_actions[-1]}_1",
            open(os.path.join(script_dir, "../test_resources/cat.jpg"), "rb").read(),
            content_type="image/png",
        )
        files = images[:]
        injected = images[:]
        injected[-1] = image_inject

        payload = make_payload_from_file(files, injected)
        response = self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}", payload
        )
        self.assertTrue(status.is_client_error(response.status_code))
        payload = make_payload_from_file(injected, files)
        response = self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}", payload
        )
        self.assertTrue(status.is_client_error(response.status_code))

    @patch("ekyc.views.liveness.upload_liveness", mock_upload_liveness_backend_failed)
    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    @patch("ekyc.models.liveness.get_result_liveness", mock_get_result_liveness)
    def test_api_liveness_pass_but_backend_check_dont(self):
        """
        Upload face images should pass
        """
        response = self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
            self.get_liveness_passed_payload(),
        )
        self.assertTrue(status.is_success(response.status_code))
        response = json.loads(response.content)
        self.check_success_response_default_format(response)

        # Ekyc Status check
        self.liveness = self.ekyc.liveness_set.first()
        self.assertEqual(self.liveness.status, STATUS_FAILED_BACKEND)

        # Ignorance Test
        self.assertEqual(
            self.ekyc.check_liveness(schema={"configs": {"ignore": {"face_compare": False, "label": False}}}),
            False,
        )
        self.assertEqual(
            self.ekyc.check_liveness(schema={"configs": {"ignore": {"face_compare": True, "label": False}}}),
            False,
        )
        self.assertEqual(
            self.ekyc.check_liveness(schema={"configs": {"ignore": {"face_compare": False, "label": True}}}),
            False,
        )
        self.assertEqual(
            self.ekyc.check_liveness(schema={"configs": {"ignore": {"face_compare": True, "label": True}}}),
            True,
        )

    @patch("ekyc.views.liveness.upload_liveness", mock_upload_liveness_face_not_found)
    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_face_not_found(self):
        """
        Upload no-face images should return status.HTTP_422_UNPROCESSABLE_ENTITY Face not found
        """
        response = self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
            self.get_liveness_passed_payload(),
        )
        self.assertTrue(status.is_client_error(response.status_code))

        # Ekyc Status check
        self.liveness = self.ekyc.liveness_set.first()
        self.assertEqual(self.liveness.status, STATUS_FAILED_BACKEND)

    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_latest_success_flow(self):
        """
        eKYC object should pick the right liveness item
        """
        ekyc = Ekyc.objects.get(ref=1)
        self.assertIsNone(ekyc.liveness)

        # self.client.post(reverse('ekyc:liveness-faceaction') + "?ref=1")  # This one should be ignored
        self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
            self.get_liveness_passed_payload(),
        )
        self.client.post(reverse("ekyc:liveness-faceaction", kwargs={"ekyc_ref": 1}))  # This one should be ignored

        self.assertEqual(len(ekyc.liveness_set.all()), 2)
        self.assertEqual(ekyc.liveness.id, self.liveness.id)

    @patch("ekyc.models.ekyc.settings.EKYC_LIVENESS_MAX_ATTEMPT", 999)
    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_status_flow(self):
        # Pending -> Cancelled = X
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_cancelled() == STATUS_PENDING
        # Pending -> Failed Front = X
        assert self.get_liveness_failed_frontend() == STATUS_PENDING
        # Pending -> Success = X
        assert self.get_liveness_success() == STATUS_PENDING
        # Pending -> Started -> Cancelled = OK
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_cancelled() == STATUS_CANCELLED  # Final
        assert self.get_liveness_success() == STATUS_CANCELLED
        # Pending -> Started -> Failed Front = OK
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_failed_frontend() == STATUS_FAILED_FRONTEND  # Final
        assert self.get_liveness_started() == STATUS_FAILED_FRONTEND
        assert self.get_liveness_success() == STATUS_FAILED_FRONTEND
        # Pending -> Started -> Failed Back = OK
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_failed_backend() == STATUS_FAILED_BACKEND  # Final
        assert self.get_liveness_started() == STATUS_FAILED_BACKEND
        assert self.get_liveness_success() == STATUS_FAILED_BACKEND
        # Pending -> Started -> Success = OK
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_success() == STATUS_SUCCESS  # Final
        assert self.get_liveness_failed_frontend() == STATUS_SUCCESS
        assert self.get_liveness_failed_backend() == STATUS_SUCCESS
        assert self.get_liveness_cancelled() == STATUS_SUCCESS
        assert self.liveness.upload_response != None

    @patch("ekyc.models.ekyc.settings.EKYC_LIVENESS_MAX_ATTEMPT", 5)
    def test_api_liveness_max_attempt(self):
        # 2
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        # 3
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        # 4
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        # 5
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        # 6 (Exceed max_attempt)
        assert self.get_liveness_pending() == STATUS_STARTED
        self.assertEqual(self.ekyc.liveness_attempt_count, 5)
        # Remove max attempt
        with patch("ekyc.models.ekyc.settings.EKYC_LIVENESS_MAX_ATTEMPT", -1):
            assert self.get_liveness_pending() == STATUS_PENDING
            assert self.get_liveness_started() == STATUS_STARTED

    @patch("ekyc.models.ekyc.settings.EKYC_LIVENESS_MAX_ATTEMPT", 5)
    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_last_attempt_racing(self):
        # 2
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        # 3
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        # 4
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        # 5
        assert self.get_liveness_pending() == STATUS_PENDING
        liveness_1 = self.liveness
        assert self.get_liveness_pending() == STATUS_PENDING
        liveness_2 = self.liveness
        self.client.post(reverse("ekyc:liveness-start", kwargs={"ekyc_ref": 1}) + f"?ref={liveness_1.id}")
        self.client.post(reverse("ekyc:liveness-start", kwargs={"ekyc_ref": 1}) + f"?ref={liveness_2.id}")
        self.assertEqual(self.ekyc.liveness_attempt_count, 5)

        liveness_1 = Liveness.objects.get(id=liveness_1.id)
        liveness_2 = Liveness.objects.get(id=liveness_2.id)
        self.assertTrue(
            (liveness_1.status == STATUS_STARTED and liveness_2.status == STATUS_PENDING)
            or (liveness_1.status == STATUS_PENDING and liveness_2.status == STATUS_STARTED)
        )

        self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={liveness_1.id}",
            self.get_liveness_passed_payload(liveness_1),
        )
        self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={liveness_2.id}",
            self.get_liveness_passed_payload(liveness_2),
        )
        liveness_1 = Liveness.objects.get(id=liveness_1.id)
        liveness_2 = Liveness.objects.get(id=liveness_2.id)

        self.assertTrue(
            (liveness_1.status == STATUS_SUCCESS and liveness_2.status == STATUS_PENDING)
            or (liveness_1.status == STATUS_PENDING and liveness_2.status == STATUS_SUCCESS)
        )

    @patch("ekyc.models.ekyc.settings.EKYC_LIVENESS_MAX_ATTEMPT", 6)
    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_webhook_ends_with_failed_backend(self):
        # 2
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.mock_call_webhook.mock_calls == [call("liveness_pending")]
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.mock_call_webhook.mock_calls == [call("liveness_started", payload={"attempt": 2})]
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_success() == STATUS_SUCCESS
        assert self.mock_call_webhook.mock_calls == [call("liveness_success", payload={"attempt": 2})]
        # 3
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_cancelled() == STATUS_CANCELLED
        assert self.mock_call_webhook.mock_calls == [
            call("liveness_pending"),
            call("liveness_started", payload={"attempt": 3}),
            call("liveness_cancelled", payload={"attempt": 3}),
        ]
        # 4
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_failed_frontend() == STATUS_FAILED_FRONTEND
        assert self.mock_call_webhook.mock_calls == [
            call("liveness_pending"),
            call("liveness_started", payload={"attempt": 4}),
            call("liveness_failed", payload={"attempt": 4}),
        ]
        # 5
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_failed_backend() == STATUS_FAILED_BACKEND
        assert self.mock_call_webhook.mock_calls == [
            call("liveness_pending"),
            call("liveness_started", payload={"attempt": 5}),
            call("liveness_failed", payload={"attempt": 5}),
        ]
        # 6
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_failed_backend() == STATUS_FAILED_BACKEND
        assert self.mock_call_webhook.mock_calls == [
            call("liveness_pending"),
            call("liveness_started", payload={"attempt": 6}),
            call("liveness_failed", payload={"attempt": 6}),
            call("liveness_reached_max_attempts"),
        ]
        # 7
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_FAILED_BACKEND
        assert self.mock_call_webhook.mock_calls == [call("liveness_reached_max_attempts")]

    @patch("ekyc.models.ekyc.settings.EKYC_LIVENESS_MAX_ATTEMPT", 3)
    def test_api_liveness_webhook_ends_with_failed_frontend(self):
        # 2
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_failed_frontend() == STATUS_FAILED_FRONTEND
        assert self.mock_call_webhook.mock_calls == [
            call("liveness_pending"),
            call("liveness_started", payload={"attempt": 2}),
            call("liveness_failed", payload={"attempt": 2}),
        ]
        # 3
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_failed_frontend() == STATUS_FAILED_FRONTEND
        assert self.mock_call_webhook.mock_calls == [
            call("liveness_pending"),
            call("liveness_started", payload={"attempt": 3}),
            call("liveness_failed", payload={"attempt": 3}),
            call("liveness_reached_max_attempts"),
        ]
        # 7
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_FAILED_FRONTEND
        assert self.mock_call_webhook.mock_calls == [call("liveness_reached_max_attempts")]

    @patch("ekyc.models.ekyc.settings.EKYC_LIVENESS_MAX_ATTEMPT", 3)
    def test_api_liveness_webhook_ends_with_cancelled(self):
        # 2
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_cancelled() == STATUS_CANCELLED
        assert self.mock_call_webhook.mock_calls == [
            call("liveness_pending"),
            call("liveness_started", payload={"attempt": 2}),
            call("liveness_cancelled", payload={"attempt": 2}),
        ]
        # 3
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED
        assert self.get_liveness_cancelled() == STATUS_CANCELLED
        assert self.mock_call_webhook.mock_calls == [
            call("liveness_pending"),
            call("liveness_started", payload={"attempt": 3}),
            call("liveness_cancelled", payload={"attempt": 3}),
            call("liveness_reached_max_attempts"),
        ]
        # 7
        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_pending() == STATUS_CANCELLED
        assert self.mock_call_webhook.mock_calls == [call("liveness_reached_max_attempts")]

    def test_liveness_log(self):
        log = {"abc": "123"}
        self.client.post(
            reverse("ekyc:liveness-faceaction", kwargs={"ekyc_ref": 1}),
            {"log": log},
            "application/json",
        )
        liveness = self.ekyc.liveness_set.last()
        assert json.loads(liveness.log) == log

    @patch("ekyc.models.liveness.get_result_liveness", mock_get_result_liveness)
    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_webhook_called_once(self):
        assert self.get_liveness_pending() == STATUS_PENDING
        assert self.get_liveness_started() == STATUS_STARTED

        self.mock_call_webhook.reset_mock()
        assert self.get_liveness_success() == STATUS_SUCCESS

        ## Check if called once
        self.liveness.fetch_report()
        self.mock_call_webhook.assert_called_once()

    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_saves_facecompare_when_top_level_face_compare_present(self):
        # Prepare a comparable front card
        from ..models import FrontCard

        FrontCard.objects.create(
            ekyc=self.ekyc,
            upload_response="ID_REQ_123",
            result_response={},
            nonce="n1",
            is_success=True,
        )

        # Custom mock that returns top-level face_compare block
        def mock_upload_with_facecompare(*args, **kwargs):
            return (
                {
                    "code": 200,
                    "status": "success",
                    "data": "2025-09-04_08-40-01_HGaEre",
                    "result": {
                        "brightness": {"status": True, "message": "passed"},
                        "face_out_frame": {"status": True, "message": "passed"},
                        "face_size": {"status": True, "message": "passed"},
                        "face_center_alignment": {"status": True, "message": "passed"},
                        "liveness_detection": {
                            "status": True,
                            "error_code": None,
                            "probability": 0.9630306,
                            "message": "passed",
                        },
                        "face_smile": {"status": True, "message": "passed"},
                        "face_mouth": {"status": True, "message": "passed"},
                        "blur_detection": {"status": True, "message": "passed"},
                        "eye_open_detection": {"status": True, "message": "passed"},
                    },
                    "face_compare": {
                        "Recognito": {},
                        "Face++": {
                            "request_id": "1756975205,aab5241c-4e57-4eb0-b6f3-663456f980be",
                            "time_used": 271,
                            "confidence": 20.155,
                            "thresholds": {"1e-3": 62.327, "1e-4": 69.101, "1e-5": 73.975},
                            "faces1": [
                                {
                                    "face_token": "a36c589baa0212176779becaff3b7e34",
                                    "face_rectangle": {"top": 377, "left": 753, "width": 85, "height": 85},
                                }
                            ],
                            "faces2": [
                                {
                                    "face_token": "984fc2398f50fa04f041313c42005907",
                                    "face_rectangle": {"top": 319, "left": 256, "width": 357, "height": 357},
                                }
                            ],
                            "image_id1": "JoU+EsPSfZblc9XoFpOsEw==",
                            "image_id2": "LkD3yWOWhohjaBZhcCCSLg==",
                        },
                        "result": {"status": "ไม่ผ่าน", "score": 20.155, "threshold": 0.74},
                        "request_id1": "2025-09-04_08-40-01_HGaEre",
                        "iv1": "GdbCDXLlG8peehB1naEA+Q==",
                        "img1": "img.jpg",
                        "request_id2": "2025-09-03_10-17-49_WTiaNR",
                        "iv2": "sOBWyTt2eMHLzZShViLlbg==",
                        "img2": "img.jpg",
                        "request_id": "2025-09-04_08-40-06_EnrsnQ",
                    },
                },
                200,
            )

        def mock_get_form_settings(form, path, default=None):
            if path == "ekyc.liveness.perform_face_compare":
                return True
            return default

        with patch("ekyc.views.liveness.get_form_settings", mock_get_form_settings):
            with patch("ekyc.views.liveness.upload_liveness", mock_upload_with_facecompare):
                response = self.client.post(
                    reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
                    self.get_liveness_passed_payload(),
                )
                self.assertTrue(status.is_success(response.status_code))
                payload = json.loads(response.content)
                # Should not suggest compare_url since facecompare saved from response
                self.assertNotIn("compare_url", payload)

                # DB assertions
                ekyc = Ekyc.objects.get(ref="1")
                self.assertIsNotNone(ekyc.face_compare)
                self.assertTrue(ekyc.face_compare.is_success)
                self.assertEqual(ekyc.face_compare.upload_response, "2025-09-04_08-40-06_EnrsnQ")
                # Ensure the linkage is correct
                self.assertEqual(ekyc.face_compare.liveness.id, self.liveness.id)
                self.assertEqual(ekyc.face_compare.front_card.ekyc_id, ekyc.id)

    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_no_top_level_face_compare_shows_compare_url(self):
        # Prepare a comparable front card
        from ..models import FrontCard

        FrontCard.objects.create(
            ekyc=self.ekyc,
            upload_response="ID_REQ_124",
            result_response={},
            nonce="n2",
            is_success=True,
        )

        # Use default mock from setUp (no top-level face_compare)
        response = self.client.post(
            reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
            self.get_liveness_passed_payload(),
        )
        self.assertTrue(status.is_success(response.status_code))
        payload = json.loads(response.content)
        # Should provide compare_url for manual/async compare flow
        self.assertIn("compare_url", payload)

        ekyc = Ekyc.objects.get(ref="1")
        self.assertIsNone(ekyc.face_compare)

    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_saves_facecompare_when_top_level_face_compare_status_failed_should_save_liveness_but_not_save_facecompare(
        self,
    ):
        """
        When top-level face_compare has status_code indicating failure,
        should save liveness but not save facecompare
        """
        # Prepare a comparable front card
        from ..models import FrontCard

        FrontCard.objects.create(
            ekyc=self.ekyc,
            upload_response="ID_REQ_125",
            result_response={},
            nonce="n3",
            is_success=True,
        )

        # Custom mock that returns top-level face_compare block with failed status_code
        def mock_upload_with_failed_facecompare(*args, **kwargs):
            return (
                {
                    "code": 200,
                    "status": "success",
                    "data": "2025-09-04_08-40-01_HGaEre",
                    "result": {
                        "brightness": {"status": True, "message": "passed"},
                        "face_out_frame": {"status": True, "message": "passed"},
                        "face_size": {"status": True, "message": "passed"},
                        "face_center_alignment": {"status": True, "message": "passed"},
                        "liveness_detection": {
                            "status": True,
                            "error_code": None,
                            "probability": 0.9630306,
                            "message": "passed",
                        },
                        "face_smile": {"status": True, "message": "passed"},
                        "face_mouth": {"status": True, "message": "passed"},
                        "blur_detection": {"status": True, "message": "passed"},
                        "eye_open_detection": {"status": True, "message": "passed"},
                    },
                    "face_compare": {
                        "status_code": 400,  # Failed status code
                        "Recognito": {},
                        "Face++": {
                            "request_id": "1756975205,aab5241c-4e57-4eb0-b6f3-663456f980be",
                            "time_used": 271,
                            "confidence": 20.155,
                            "thresholds": {"1e-3": 62.327, "1e-4": 69.101, "1e-5": 73.975},
                        },
                        "result": {"status": "ไม่ผ่าน", "score": 20.155, "threshold": 0.74},
                        "request_id": "2025-09-04_08-40-06_EnrsnQ",
                    },
                },
                200,
            )

        def mock_get_form_settings(form, path, default=None):
            if path == "ekyc.liveness.perform_face_compare":
                return True
            return default

        with patch("ekyc.views.liveness.get_form_settings", mock_get_form_settings):
            with patch("ekyc.views.liveness.upload_liveness", mock_upload_with_failed_facecompare):
                response = self.client.post(
                    reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
                    self.get_liveness_passed_payload(),
                )
                self.assertTrue(status.is_success(response.status_code))
                payload = json.loads(response.content)
                # Should provide compare_url since facecompare failed to save
                self.assertIn("compare_url", payload)

                # DB assertions
                ekyc = Ekyc.objects.get(ref="1")
                # Liveness should be saved successfully
                self.assertIsNotNone(ekyc.liveness)
                self.assertTrue(ekyc.liveness.is_success)
                # Face compare should NOT be saved due to failed status_code
                self.assertIsNone(ekyc.face_compare)

    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_saves_facecompare_when_top_level_face_compare_missing_request_id_should_save_liveness_but_not_save_facecompare(
        self,
    ):
        """
        When top-level face_compare is present but missing request_id,
        should save liveness but not save facecompare
        """
        # Prepare a comparable front card
        from ..models import FrontCard

        FrontCard.objects.create(
            ekyc=self.ekyc,
            upload_response="ID_REQ_126",
            result_response={},
            nonce="n4",
            is_success=True,
        )

        # Custom mock that returns top-level face_compare block without request_id
        def mock_upload_with_incomplete_facecompare(*args, **kwargs):
            return (
                {
                    "code": 200,
                    "status": "success",
                    "data": "2025-09-04_08-40-01_HGaEre",
                    "result": {
                        "brightness": {"status": True, "message": "passed"},
                        "face_out_frame": {"status": True, "message": "passed"},
                        "face_size": {"status": True, "message": "passed"},
                        "face_center_alignment": {"status": True, "message": "passed"},
                        "liveness_detection": {
                            "status": True,
                            "error_code": None,
                            "probability": 0.9630306,
                            "message": "passed",
                        },
                        "face_smile": {"status": True, "message": "passed"},
                        "face_mouth": {"status": True, "message": "passed"},
                        "blur_detection": {"status": True, "message": "passed"},
                        "eye_open_detection": {"status": True, "message": "passed"},
                    },
                    "face_compare": {
                        "Recognito": {},
                        "Face++": {
                            "request_id": "1756975205,aab5241c-4e57-4eb0-b6f3-663456f980be",
                            "time_used": 271,
                            "confidence": 20.155,
                            "thresholds": {"1e-3": 62.327, "1e-4": 69.101, "1e-5": 73.975},
                        },
                        "result": {"status": "ผ่าน", "score": 85.155, "threshold": 0.74},
                        # Missing request_id field
                    },
                },
                200,
            )

        def mock_get_form_settings(form, path, default=None):
            if path == "ekyc.liveness.perform_face_compare":
                return True
            return default

        with patch("ekyc.views.liveness.get_form_settings", mock_get_form_settings):
            with patch("ekyc.views.liveness.upload_liveness", mock_upload_with_incomplete_facecompare):
                response = self.client.post(
                    reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
                    self.get_liveness_passed_payload(),
                )
                self.assertTrue(status.is_success(response.status_code))
                payload = json.loads(response.content)
                # Should provide compare_url since facecompare failed to save
                self.assertIn("compare_url", payload)

                # DB assertions
                ekyc = Ekyc.objects.get(ref="1")
                # Liveness should be saved successfully
                self.assertIsNotNone(ekyc.liveness)
                self.assertTrue(ekyc.liveness.is_success)
                # Face compare should NOT be saved due to missing request_id
                self.assertIsNone(ekyc.face_compare)

    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_saves_facecompare_when_top_level_face_compare_successful_should_save_both_liveness_and_facecompare(
        self,
    ):
        """
        When top-level face_compare is present and successful,
        should save both liveness and facecompare
        """
        # Prepare a comparable front card
        from ..models import FrontCard

        FrontCard.objects.create(
            ekyc=self.ekyc,
            upload_response="ID_REQ_127",
            result_response={},
            nonce="n5",
            is_success=True,
        )

        # Custom mock that returns top-level face_compare block with successful result
        def mock_upload_with_successful_facecompare(*args, **kwargs):
            return (
                {
                    "code": 200,
                    "status": "success",
                    "data": "2025-09-04_08-40-01_HGaEre",
                    "result": {
                        "brightness": {"status": True, "message": "passed"},
                        "face_out_frame": {"status": True, "message": "passed"},
                        "face_size": {"status": True, "message": "passed"},
                        "face_center_alignment": {"status": True, "message": "passed"},
                        "liveness_detection": {
                            "status": True,
                            "error_code": None,
                            "probability": 0.9630306,
                            "message": "passed",
                        },
                        "face_smile": {"status": True, "message": "passed"},
                        "face_mouth": {"status": True, "message": "passed"},
                        "blur_detection": {"status": True, "message": "passed"},
                        "eye_open_detection": {"status": True, "message": "passed"},
                    },
                    "face_compare": {
                        "status_code": 200,  # Successful status code
                        "Recognito": {},
                        "Face++": {
                            "request_id": "1756975205,aab5241c-4e57-4eb0-b6f3-663456f980be",
                            "time_used": 271,
                            "confidence": 85.155,
                            "thresholds": {"1e-3": 62.327, "1e-4": 69.101, "1e-5": 73.975},
                        },
                        "result": {"status": "ผ่าน", "score": 85.155, "threshold": 0.74},
                        "request_id": "2025-09-04_08-40-06_EnrsnQ",
                    },
                },
                200,
            )

        def mock_get_form_settings(form, path, default=None):
            if path == "ekyc.liveness.perform_face_compare":
                return True
            return default

        with patch("ekyc.views.liveness.get_form_settings", mock_get_form_settings):
            with patch("ekyc.views.liveness.upload_liveness", mock_upload_with_successful_facecompare):
                response = self.client.post(
                    reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
                    self.get_liveness_passed_payload(),
                )
                self.assertTrue(status.is_success(response.status_code))
                payload = json.loads(response.content)
                # Should NOT provide compare_url since facecompare was saved successfully
                self.assertNotIn("compare_url", payload)

                # DB assertions
                ekyc = Ekyc.objects.get(ref="1")
                # Liveness should be saved successfully
                self.assertIsNotNone(ekyc.liveness)
                self.assertTrue(ekyc.liveness.is_success)
                # Face compare should be saved successfully
                self.assertIsNotNone(ekyc.face_compare)
                self.assertTrue(ekyc.face_compare.is_success)
                self.assertEqual(ekyc.face_compare.upload_response, "2025-09-04_08-40-06_EnrsnQ")
                # Ensure the linkage is correct
                self.assertEqual(ekyc.face_compare.liveness.id, self.liveness.id)
                self.assertEqual(ekyc.face_compare.front_card.ekyc_id, ekyc.id)

    @patch("ekyc.views.liveness.KnownFaceApp", KnownFaceAppMock)
    def test_api_liveness_saves_facecompare_when_top_level_face_compare_no_perform_setting_should_not_save_facecompare(
        self,
    ):
        """
        When top-level face_compare is present but perform_face_compare setting is False,
        should save liveness but not save facecompare
        """
        # Prepare a comparable front card
        from ..models import FrontCard

        FrontCard.objects.create(
            ekyc=self.ekyc,
            upload_response="ID_REQ_128",
            result_response={},
            nonce="n6",
            is_success=True,
        )

        # Custom mock that returns top-level face_compare block
        def mock_upload_with_facecompare_no_setting(*args, **kwargs):
            return (
                {
                    "code": 200,
                    "status": "success",
                    "data": "2025-09-04_08-40-01_HGaEre",
                    "result": {
                        "brightness": {"status": True, "message": "passed"},
                        "face_out_frame": {"status": True, "message": "passed"},
                        "face_size": {"status": True, "message": "passed"},
                        "face_center_alignment": {"status": True, "message": "passed"},
                        "liveness_detection": {
                            "status": True,
                            "error_code": None,
                            "probability": 0.9630306,
                            "message": "passed",
                        },
                        "face_smile": {"status": True, "message": "passed"},
                        "face_mouth": {"status": True, "message": "passed"},
                        "blur_detection": {"status": True, "message": "passed"},
                        "eye_open_detection": {"status": True, "message": "passed"},
                    },
                    "face_compare": {
                        "status_code": 200,
                        "Recognito": {},
                        "Face++": {
                            "request_id": "1756975205,aab5241c-4e57-4eb0-b6f3-663456f980be",
                            "time_used": 271,
                            "confidence": 85.155,
                            "thresholds": {"1e-3": 62.327, "1e-4": 69.101, "1e-5": 73.975},
                        },
                        "result": {"status": "ผ่าน", "score": 85.155, "threshold": 0.74},
                        "request_id": "2025-09-04_08-40-06_EnrsnQ",
                    },
                },
                200,
            )

        def mock_get_form_settings(form, path, default=None):
            if path == "ekyc.liveness.perform_face_compare":
                return False  # Setting is disabled
            return default

        with patch("ekyc.views.liveness.get_form_settings", mock_get_form_settings):
            with patch("ekyc.views.liveness.upload_liveness", mock_upload_with_facecompare_no_setting):
                response = self.client.post(
                    reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": 1}) + f"?ref={self.liveness.id}",
                    self.get_liveness_passed_payload(),
                )
                self.assertTrue(status.is_success(response.status_code))
                payload = json.loads(response.content)
                # Should provide compare_url since perform_face_compare is disabled
                self.assertIn("compare_url", payload)

                # DB assertions
                ekyc = Ekyc.objects.get(ref="1")
                # Liveness should be saved successfully
                self.assertIsNotNone(ekyc.liveness)
                self.assertTrue(ekyc.liveness.is_success)
                # Face compare should NOT be saved due to disabled setting
                self.assertIsNone(ekyc.face_compare)
