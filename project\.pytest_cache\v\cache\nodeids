["app/auth/tests/test_logout_jwt.py::TestLogoutJWT::test_logout_jwt_cannot_reuse_token", "app/auth/tests/test_secret_manager.py::SecretManagerTest::test_has_key_in_secret_manager", "app/auth/tests/test_secret_manager.py::SecretManagerTest::test_not_has_key_in_env_and_default_not_found_in_secret_manager", "app/auth/tests/test_secret_manager.py::SecretManagerTest::test_not_has_key_in_env_but_default_found_in_secret_manager", "app/auth/tests/test_secret_manager.py::SecretManagerTest::test_not_has_key_in_secret_manager", "app/auth/tests/test_set_password.py::TestSetPassword::test_can_change_password", "app/auth/tests/test_set_password.py::TestSetPassword::test_lock_change_password", "app/auth/tests/test_set_password.py::TestSetPassword::test_lock_duration_flow", "app/tests/test_custom_http_header.py::TestVerifyQueryDict::test_return_custom_http_headers", "app/tests/test_user_encode_token.py::TestUserEncodeToken::test_encode_with_exp", "app/tests/test_user_encode_token.py::TestUserEncodeToken::test_encode_without_expired", "app/tests/test_user_serializer.py::TestUserDetailsSerializer::test_none_super_user", "app/tests/test_user_serializer.py::TestUserDetailsSerializer::test_super_user", "app/tests/test_verify_query.py::TestVerifyQueryDict::test_query_dict_no_t", "app/tests/test_verify_query.py::TestVerifyQueryDict::test_query_dict_with_blank_t", "app/tests/test_verify_query.py::TestVerifyQueryDict::test_query_dict_with_t", "appsetting/tests/test_api.py::SettingsTests::test_delete", "appsetting/tests/test_api.py::SettingsTests::test_get_info", "appsetting/tests/test_api.py::SettingsTests::test_get_input_form", "appsetting/tests/test_api.py::SettingsTests::test_get_list", "appsetting/tests/test_api.py::SettingsTests::test_get_list_for_admin", "appsetting/tests/test_api.py::SettingsTests::test_get_list_for_edit", "appsetting/tests/test_api.py::SettingsTests::test_post_create", "appsetting/tests/test_api.py::SettingsTests::test_post_update", "appsetting/tests/test_api.py::SettingsTests::test_post_update_batch", "appsetting/tests/test_api.py::SettingsTests::test_run_possible_command_only", "appsetting/tests/test_command.py::SettingsTests::test_initsettings", "appsetting/tests/test_command.py::SettingsTests::test_readenvsettings", "appsetting/tests/test_inject.py::SettingsTests::test_injection_header", "appsetting/tests/test_inject.py::SettingsTests::test_overridden_value", "bankstatement/tests/test_api_v2.py::BankstatementV2Tests::test_api_result", "bankstatement/tests/test_api_v2.py::BankstatementV2Tests::test_api_upload", "bankstatement/tests/test_api_v2.py::BankstatementV2Tests::test_create_batch_no_duplicate", "bankstatement/tests/test_api_v2.py::BankstatementV2Tests::test_flag_task", "bankstatement/tests/test_support_bank_list.py::BankStatementSupportBankListTests::test_get_support_list_default", "bankstatement/tests/test_support_bank_list.py::BankStatementSupportBankListTests::test_get_support_list_with_invalid_lang_params", "bankstatement/tests/test_support_bank_list.py::BankStatementSupportBankListTests::test_get_support_list_with_params", "bankstatement/tests/test_validate_bank_statement.py::BankStatementDocumentTests::test_validation_empty_documents", "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_only_first_name_match", "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validate_document", "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validation_name_match", "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validation_producer_check", "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validation_same_creation_mod_date", "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validation_valid_account_number", "data_point/tests/comply_advantage/test_comply_advantage.py::TestComplyAdvantage::test_comply_advantage", "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_call_DBD_and_Creden", "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_call_DBD_and_C<PERSON>en_and_all_shareholder_case_1_and_3", "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_call_DBD_and_C<PERSON>en_and_all_shareholder_case_2_and_3", "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_call_DBD_and_<PERSON><PERSON><PERSON>_and_some_shareholder_from_input_name_case_1_and_2", "data_point/tests/dbd_share_holder/test_dbd_share_holder.py::TestDBDShareholder::test_dbd_share_holder_list_of_full_name_is_None_case_1_and_3", "data_point/tests/provider/test_amlo.py::TestAMLO::test_amlo_person_screening_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_amlo_person_screening_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_amlo_person_screening_un_list_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_amlo_person_screening_un_list_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_input_validation[FAILED_VALIDATE: Characters contain-test_case_input6-expected_result6]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_input_validation[FAILED_VALIDATE: Invalid checksum-test_case_input2-expected_result2]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_input_validation[FAILED_VALIDATE: Less 13 Digit-test_case_input3-expected_result3]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_input_validation[FAILED_VALIDATE: More 13 Digit-test_case_input4-expected_result4]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_input_validation[FAILED_VALIDATE: National id None value-test_case_input5-expected_result5]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_input_validation[FAILED_VALIDATE: No Full Name-test_case_input1-expected_result1]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_input_validation[FAILED_VALIDATE: None value-test_case_input7-expected_result7]", "data_point/tests/provider/test_amlo.py::TestAMLO::test_input_validation[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_answer.py::TestAnswer::test_is_full_address[FOUND-test_case_input0-True]", "data_point/tests/provider/test_answer.py::TestAnswer::test_is_full_address[NOT_FOUND-test_case_input1-False]", "data_point/tests/provider/test_answer_2.py::TestAnswerWithDB::test_get_form_local_set", "data_point/tests/provider/test_answer_2.py::TestAnswerWithDB::test_get_item_label", "data_point/tests/provider/test_answer_2.py::TestAnswerWithDB::test_set_form_item_labels", "data_point/tests/provider/test_application_status.py::TestApplicationStatus::test_get_options[HAS_EKYC-test_case_input0-expected_result0]", "data_point/tests/provider/test_application_status.py::TestApplicationStatus::test_get_options[NO_EKYC-test_case_input1-expected_result1]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_asia_verify_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_asia_verify_api_service[FAILED_VALIDATE: handle no data found in response-test_case_input2-expected_result2]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_asia_verify_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_data_point_json_field", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_datapoint_option_result_from_asia_verify[SUCCESS_VALIDATE: ASIA_VERIFY_HKG-test_case_input3-expected_result3]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_datapoint_option_result_from_asia_verify[SUCCESS_VALIDATE: ASIA_VERIFY_JPN-test_case_input0-expected_result0]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_datapoint_option_result_from_asia_verify[SUCCESS_VALIDATE: ASIA_VERIFY_MYS-test_case_input1-expected_result1]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_datapoint_option_result_from_asia_verify[SUCCESS_VALIDATE: ASIA_VERIFY_SGP-test_case_input2-expected_result2]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_get_options", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[FAILED_VALIDATE: None Input-test_case_input5-expected_result5]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[FAILED_VALIDATE: None Input-test_case_input7-expected_result7]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[FAILED_VALIDATE: None company_id-test_case_input4-expected_result4]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[FAILED_VALIDATE: None company_id-test_case_input6-expected_result6]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[FAILED_VALIDATE: None country-test_case_input3-expected_result3]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[FAILED_VALIDATE: None country-test_case_input5-expected_result5]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[FAILED_VALIDATE: Not country in country select-test_case_input6-expected_result6]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[FAILED_VALIDATE: Not country in country select-test_case_input8-expected_result8]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[SUCCESS_VALIDATE: Japan case-test_case_input0-expected_result0]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[SUCCESS_VALIDATE: Malaysia case-test_case_input1-expected_result1]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[SUCCESS_VALIDATE: Philipine case-test_case_input3-expected_result3]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[SUCCESS_VALIDATE: Philipine case-test_case_input4-expected_result4]", "data_point/tests/provider/test_asia_verify.py::TestAsiaVerifyBusiness::test_input_validation[SUCCESS_VALIDATE: Singapore case-test_case_input2-expected_result2]", "data_point/tests/provider/test_base_get_options.py::TestBaseGetOptions::test_get_options_enable_form_settings", "data_point/tests/provider/test_base_get_options.py::TestBaseGetOptions::test_is_enable_data_point_option", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_asia_verify_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_asia_verify_api_service[FAILED_VALIDATE: handle no data found in response-test_case_input2-expected_result2]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_asia_verify_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_japan[SUCCESS_VALIDATE: ASIA_VERIFY_JPN: ALL_HIT-test_case_input3-expected_result3]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_japan[SUCCESS_VALIDATE: ASIA_VERIFY_JPN: COMPANY_HIT-test_case_input0-expected_result0]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_japan[SUCCESS_VALIDATE: ASIA_VERIFY_JPN: DIRECTOR_HIT-test_case_input1-expected_result1]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_japan[SUCCESS_VALIDATE: ASIA_VERIFY_JPN: SHAREHOLDER_HIT-test_case_input2-expected_result2]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_malaysia[SUCCESS_VALIDATE: ASIA_VERIFY_MYS: ALL_HIT-test_case_input3-expected_result3]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_malaysia[SUCCESS_VALIDATE: ASIA_VERIFY_MYS: COMPANY_HIT-test_case_input0-expected_result0]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_malaysia[SUCCESS_VALIDATE: ASIA_VERIFY_MYS: DIRECTOR_HIT-test_case_input1-expected_result1]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_malaysia[SUCCESS_VALIDATE: ASIA_VERIFY_MYS: SHAREHOLDER_HIT-test_case_input2-expected_result2]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_singapore[SUCCESS_VALIDATE: ASIA_VERIFY_SGP: ALL_HIT-test_case_input3-expected_result3]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_singapore[SUCCESS_VALIDATE: ASIA_VERIFY_SGP: COMPANY_HIT-test_case_input0-expected_result0]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_singapore[SUCCESS_VALIDATE: ASIA_VERIFY_SGP: DIRECTOR_HIT-test_case_input1-expected_result1]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_datapoint_option_result_from_asia_verify_singapore[SUCCESS_VALIDATE: ASIA_VERIFY_SGP: SHAREHOLDER_HIT-test_case_input2-expected_result2]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_dbd_api_service[FAILED_VALIDATE: 404 not found: COMPLY_ADVANTAGE case-test_case_input5-expected_result5]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_dbd_api_service[FAILED_VALIDATE: 404 not found: CREDEN case-test_case_input3-expected_result3]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_dbd_api_service[FAILED_VALIDATE: 404 not found: DBD case-test_case_input1-expected_result1]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_dbd_api_service[SUCCESS_VALIDATE: COMPLY_ADVANTAGE case-test_case_input4-expected_result4]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_dbd_api_service[SUCCESS_VALIDATE: CREDEN case-test_case_input2-expected_result2]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_dbd_api_service[SUCCESS_VALIDATE: DBD case-test_case_input0-expected_result0]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_initial_data", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[FAILED_VALIDATE: Company id is None value-test_case_input7-expected_result7]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[FAILED_VALIDATE: Company id is contain characters for Thailand company.-test_case_input8-expected_result8]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[FAILED_VALIDATE: Company id is less then 13 digits for Thailand company.-test_case_input5-expected_result5]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[FAILED_VALIDATE: Company id is more then 13 digits for Thailand company.-test_case_input6-expected_result6]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[FAILED_VALIDATE: Country do not support.-test_case_input9-expected_result9]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[SUCCESS_VALIDATE: Hongkong case-test_case_input4-expected_result4]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[SUCCESS_VALIDATE: Japan case-test_case_input1-expected_result1]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[SUCCESS_VALIDATE: Malaysia case-test_case_input2-expected_result2]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[SUCCESS_VALIDATE: Singapore case-test_case_input3-expected_result3]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_input_validation[SUCCESS_VALIDATE: Thailand case-test_case_input0-expected_result0]", "data_point/tests/provider/test_business_aml.py::TestBusinessAML::test_juristic_id_info", "data_point/tests/provider/test_business_aml_data_point.py::TestBusinessAMLDataPoint::test_is_any_25pct_shareholders_th_amlo_hits_freeze_05", "data_point/tests/provider/test_business_aml_data_point.py::TestBusinessAMLDataPoint::test_is_any_25pct_shareholders_th_amlo_hits_un_sanction", "data_point/tests/provider/test_business_aml_data_point.py::TestBusinessAMLDataPoint::test_is_any_25pct_shareholders_th_led_bankrupt", "data_point/tests/provider/test_business_aml_data_point.py::TestBusinessAMLDataPoint::test_is_any_25pct_shareholders_with_hits", "data_point/tests/provider/test_business_aml_data_point.py::TestBusinessAMLDataPoint::test_is_any_directors_th_amlo_hits", "data_point/tests/provider/test_business_aml_data_point.py::TestBusinessAMLDataPoint::test_is_any_directors_th_amlo_hits_un_sanction", "data_point/tests/provider/test_business_aml_data_point.py::TestBusinessAMLDataPoint::test_is_any_directors_th_pep_hits", "data_point/tests/provider/test_business_aml_data_point.py::TestBusinessAMLDataPoint::test_is_any_directors_with_hits", "data_point/tests/provider/test_comply_advantage.py::TestComplyAdvantage::test_input_validation[FAILED_VALIDATE: No first name-test_case_input4-expected_result4]", "data_point/tests/provider/test_comply_advantage.py::TestComplyAdvantage::test_input_validation[FAILED_VALIDATE: No full_name-test_case_input3-expected_result3]", "data_point/tests/provider/test_comply_advantage.py::TestComplyAdvantage::test_input_validation[FAILED_VALIDATE: No last name-test_case_input5-expected_result5]", "data_point/tests/provider/test_comply_advantage.py::TestComplyAdvantage::test_input_validation[FAILED_VALIDATE: Not a date pattern-test_case_input6-expected_result6]", "data_point/tests/provider/test_comply_advantage.py::TestComplyAdvantage::test_input_validation[FAILED_VALIDATE: dob is current-test_case_input8-expected_result8]", "data_point/tests/provider/test_comply_advantage.py::TestComplyAdvantage::test_input_validation[FAILED_VALIDATE: dob is future-test_case_input7-expected_result7]", "data_point/tests/provider/test_comply_advantage.py::TestComplyAdvantage::test_input_validation[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_comply_advantage.py::TestComplyAdvantage::test_input_validation[SUCCESS_VALIDATE-test_case_input1-expected_result1]", "data_point/tests/provider/test_comply_advantage.py::TestComplyAdvantage::test_input_validation[SUCCESS_VALIDATE-test_case_input2-expected_result2]", "data_point/tests/provider/test_custom_status.py::TestCustomStatus::test_get_options[HAS_CUSTOM_STATUS-test_case_input0-expected_result0]", "data_point/tests/provider/test_custom_status.py::TestCustomStatus::test_get_options[NO_CUSTOM_STATUS-test_case_input1-expected_result1]", "data_point/tests/provider/test_custom_status.py::TestCustomStatus::test_get_value_options[HAS_CUSTOM_STATUS-test_case_input0-expected_result0]", "data_point/tests/provider/test_custom_status.py::TestCustomStatus::test_get_value_options[NO_CUSTOM_STATUS-test_case_input1-expected_result1]", "data_point/tests/provider/test_dbd_business.py::TestDBDBusiness::test_dbd_business_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_dbd_business.py::TestDBDBusiness::test_dbd_business_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_dbd_business.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: Characters contain-123456789None-expected_result4]", "data_point/tests/provider/test_dbd_business.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: Less 13 Digit-************-expected_result1]", "data_point/tests/provider/test_dbd_business.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: More 13 Digit-************34-expected_result2]", "data_point/tests/provider/test_dbd_business.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: None value-None-expected_result3]", "data_point/tests/provider/test_dbd_business.py::TestDBDBusiness::test_input_validation[SUCCESS_VALIDATE-************3-expected_result0]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_data_point_json_field", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_datapoint_option_result_from_dbd_directorship[SUCCESS_VALIDATE: Found case with complicate only-test_case_input3-expected_result3]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_datapoint_option_result_from_dbd_directorship[SUCCESS_VALIDATE: Found case with match only-test_case_input2-expected_result2]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_datapoint_option_result_from_dbd_directorship[SUCCESS_VALIDATE: Found case-test_case_input1-expected_result1]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_datapoint_option_result_from_dbd_directorship[SUCCESS_VALIDATE: None case-test_case_input4-expected_result4]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_datapoint_option_result_from_dbd_directorship[SUCCESS_VALIDATE: Not found case-test_case_input0-expected_result0]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_dbd_directorship_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_dbd_directorship_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_full_name_info", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_input_validation[FAILED_VALIDATE: full_name None only-test_case_input4-expected_result4]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_input_validation[FAILED_VALIDATE: full_name None value-test_case_input6-expected_result6]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_input_validation[FAILED_VALIDATE: juristic_id None value-test_case_input5-expected_result5]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_input_validation[FAILED_VALIDATE: juristic_id less 13 Digit-test_case_input2-expected_result2]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_input_validation[FAILED_VALIDATE: juristic_id more 13 Digit-test_case_input3-expected_result3]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_input_validation[SUCCESS_VALIDATE: multiple full_name-test_case_input1-expected_result1]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_input_validation[SUCCESS_VALIDATE: single full_name-test_case_input0-expected_result0]", "data_point/tests/provider/test_dbd_directorship.py::TestDBDDirectorship::test_juristic_id_info", "data_point/tests/provider/test_dbd_shareholders.py::TestDBDShareholders::test_dbd_api_service[FAILED_VALIDATE: 404 not found: CREDEN case-test_case_input3-expected_result3]", "data_point/tests/provider/test_dbd_shareholders.py::TestDBDShareholders::test_dbd_api_service[FAILED_VALIDATE: 404 not found: DBD case-test_case_input1-expected_result1]", "data_point/tests/provider/test_dbd_shareholders.py::TestDBDShareholders::test_dbd_api_service[SUCCESS_VALIDATE: CREDEN case-test_case_input2-expected_result2]", "data_point/tests/provider/test_dbd_shareholders.py::TestDBDShareholders::test_dbd_api_service[SUCCESS_VALIDATE: DBD case-test_case_input0-expected_result0]", "data_point/tests/provider/test_dbd_shareholders.py::TestDBDShareholders::test_input_validation[FAILED_VALIDATE: NOT_REQUIRE_FULL_NAME: invalid data point that not connect full_name-test_case_input4-expected_result4]", "data_point/tests/provider/test_dbd_shareholders.py::TestDBDShareholders::test_input_validation[FAILED_VALIDATE: juristic_id None value-test_case_input3-expected_result3]", "data_point/tests/provider/test_dbd_shareholders.py::TestDBDShareholders::test_input_validation[FAILED_VALIDATE: juristic_id less 13 Digit-test_case_input1-expected_result1]", "data_point/tests/provider/test_dbd_shareholders.py::TestDBDShareholders::test_input_validation[FAILED_VALIDATE: juristic_id more 13 Digit-test_case_input2-expected_result2]", "data_point/tests/provider/test_dbd_shareholders.py::TestDBDShareholders::test_input_validation[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_dopa_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_dopa_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: Empty dob-test_case_input5-expected_result5]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: Empty first_name-test_case_input3-expected_result3]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: Empty laser_id-test_case_input7-expected_result7]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: Empty last_name-test_case_input4-expected_result4]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: Empty national_id-test_case_input6-expected_result6]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: Laser id is not a pattern-test_case_input15-expected_result15]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: National id contain characters.-test_case_input14-expected_result14]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: National id has More 13 Digit-test_case_input12-expected_result12]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: National id has less 13 Digit-test_case_input11-expected_result11]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: National id is None value-test_case_input13-expected_result13]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: Not a date pattern-test_case_input8-expected_result8]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: dob is current-test_case_input10-expected_result10]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: dob is future-test_case_input9-expected_result9]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[FAILED_VALIDATE: national_id invalid checksum-test_case_input2-expected_result2]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_dopa.py::TestDBDBusiness::test_input_validation[SUCCESS_VALIDATE: Laser id lower case letters-test_case_input1-expected_result1]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_dopa_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_dopa_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: Empty dob-test_case_input5-expected_result5]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: Empty first_name-test_case_input3-expected_result3]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: Empty laser_id-test_case_input7-expected_result7]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: Empty last_name-test_case_input4-expected_result4]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: Empty national_id-test_case_input6-expected_result6]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: Laser id is not a pattern-test_case_input15-expected_result15]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: National id contain characters.-test_case_input14-expected_result14]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: National id has More 13 Digit-test_case_input12-expected_result12]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: National id has less 13 Digit-test_case_input11-expected_result11]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: National id is None value-test_case_input13-expected_result13]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: Not a date pattern-test_case_input8-expected_result8]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: dob is current-test_case_input10-expected_result10]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: dob is future-test_case_input9-expected_result9]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[FAILED_VALIDATE: national_id invalid checksum-test_case_input2-expected_result2]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[SUCCESS_VALIDATE-test_case_input0-None]", "data_point/tests/provider/test_dopa.py::TestDopaBusiness::test_input_validation[SUCCESS_VALIDATE: Laser id lower case letters-test_case_input1-None]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ALL_BANKRUPT_FOUND-test_case_input1-expected_result1]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ALL_BANKRUPT_FOUND-test_case_input5-expected_result5]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ALL_CORRUPTION_FOUND-test_case_input2-expected_result2]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ALL_CORRUPTION_FOUND-test_case_input7-expected_result7]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ALL_FOUND-test_case_input3-expected_result3]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ALL_FOUND-test_case_input9-expected_result9]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ALL_NOT_FOUND-test_case_input0-expected_result0]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ALL_NOT_FOUND-test_case_input4-expected_result4]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ANY_BANKRUPT_FOUND-test_case_input6-expected_result6]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ANY_CORRUPTION_FOUND-test_case_input8-expected_result8]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result[ANY_FOUND-test_case_input10-expected_result10]", "data_point/tests/provider/test_led.py::TestLED::test_data_point_options_result_failed[ZERO_NATIONAL_ID-test_case_input0-LED: National ID cannot be empty]", "data_point/tests/provider/test_led.py::TestLED::test_input_validation[FAILED_VALIDATE: Characters contain-123456789None-expected_result5]", "data_point/tests/provider/test_led.py::TestLED::test_input_validation[FAILED_VALIDATE: Invalid checksum-************3-expected_result1]", "data_point/tests/provider/test_led.py::TestLED::test_input_validation[FAILED_VALIDATE: Less 13 Digit-************-expected_result2]", "data_point/tests/provider/test_led.py::TestLED::test_input_validation[FAILED_VALIDATE: More 13 Digit-************34-expected_result3]", "data_point/tests/provider/test_led.py::TestLED::test_input_validation[FAILED_VALIDATE: None value-None-expected_result4]", "data_point/tests/provider/test_led.py::TestLED::test_input_validation[SUCCESS_VALIDATE-*************-expected_result0]", "data_point/tests/provider/test_led.py::TestLED::test_led_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_led.py::TestLED::test_led_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_BANKRUPT_FOUND-test_case_input1-expected_result1]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_BANKRUPT_FOUND-test_case_input12-expected_result12]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_BANKRUPT_FOUND-test_case_input5-expected_result5]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_CORRUPTION_FOUND-test_case_input14-expected_result14]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_CORRUPTION_FOUND-test_case_input2-expected_result2]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_CORRUPTION_FOUND-test_case_input7-expected_result7]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_FOUND-test_case_input3-expected_result3]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_FOUND-test_case_input9-expected_result9]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_NOT_FOUND-test_case_input0-expected_result0]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_NOT_FOUND-test_case_input11-expected_result11]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_NOT_FOUND-test_case_input4-expected_result4]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_BANKRUPT_FOUND-test_case_input13-expected_result13]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_BANKRUPT_FOUND-test_case_input6-expected_result6]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_CORRUPTION_FOUND-test_case_input11-expected_result11]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_CORRUPTION_FOUND-test_case_input15-expected_result15]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_CORRUPTION_FOUND-test_case_input8-expected_result8]", "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_FOUND-test_case_input10-expected_result10]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ALL_FOUND-test_case_input1-expected_result1]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ALL_HOUSE_OF_REPRESENTATIVE-test_case_input3-expected_result3]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ALL_HOUSE_OF_REPRESENTATIVE-test_case_input5-expected_result5]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ALL_MINISTER-test_case_input9-expected_result9]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ALL_POLICE_ARMFORCES-test_case_input11-expected_result11]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ALL_POLICE_ARMFORCES-test_case_input13-expected_result13]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ALL_PRESIDENT_OF_PROVINCIAL-test_case_input7-expected_result7]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ALL_PRESIDENT_OF_SUBDISTRICT-test_case_input15-expected_result15]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ALL_SENATOR-test_case_input17-expected_result17]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ANY_FOUND-test_case_input0-expected_result0]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ANY_HOUSE_OF_REPRESENTATIVE-test_case_input2-expected_result2]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ANY_HOUSE_OF_REPRESENTATIVE-test_case_input4-expected_result4]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ANY_MINISTER-test_case_input8-expected_result8]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ANY_POLICE_ARMFORCES-test_case_input10-expected_result10]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ANY_POLICE_ARMFORCES-test_case_input12-expected_result12]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ANY_PRESIDENT_OF_PROVINCIAL-test_case_input6-expected_result6]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ANY_PRESIDENT_OF_SUBDISTRICT-test_case_input14-expected_result14]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_data_point_options_result[ANY_SENATOR-test_case_input16-expected_result16]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_input_validation[FAILED_VALIDATE: None value-test_case_input2-expected_result2]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_input_validation[FAILED_VALIDATE: empty value-test_case_input3-expected_result3]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_input_validation[FAILED_VALIDATE: last name not match-test_case_input4-expected_result4]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_input_validation[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_input_validation[SUCCESS_VALIDATE: last name not match-test_case_input1-expected_result1]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_local_thai_pep_api_service[FAILED_VALIDATE: 404 not found.-test_case_input1-expected_result1]", "data_point/tests/provider/test_local_thai_pep_list.py::TestLocalThaiPepList::test_local_thai_pep_api_service[SUCCESS_VALIDATE-test_case_input0-expected_result0]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ALL: bankrupt_case case-test_case_input0-expected_result0]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ALL: bankruptcy_canceled case-test_case_input6-expected_result6]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ALL: currently_bankrupt case-test_case_input2-expected_result2]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ALL: ever_declared_bankrupt case-test_case_input10-expected_result10]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ALL: potential_bankrupt case-test_case_input4-expected_result4]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ALL: rehabilitation_plan case-test_case_input8-expected_result8]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ANY: bankrupt_case case-test_case_input1-expected_result1]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ANY: bankruptcy_canceled case-test_case_input7-expected_result7]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ANY: currently_bankrupt case-test_case_input3-expected_result3]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ANY: ever_declared_bankrupt case-test_case_input11-expected_result11]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ANY: potential_bankrupt case-test_case_input5-expected_result5]", "data_point/tests/provider/test_thai_bankruptcy_screening.py::TestLED::test_data_point_options_result[ANY: rehabilitation_plan case-test_case_input9-expected_result9]", "data_point/tests/provider/test_ubo.py::TestUBO::test_can_get_data_result_full_flow", "data_point/tests/provider/test_ubo.py::TestUBO::test_can_not_get_data_result_not_support_values_list", "data_point/tests/provider/test_ubo.py::TestUBO::test_exception_ca", "data_point/tests/provider/test_ubo.py::TestUBO::test_exception_ubo", "data_point/tests/provider/test_ubo.py::TestUBO::test_exception_validation", "data_point/tests/provider/test_ubo.py::TestUBO::test_get_data_result_is_all_provided_shareholders_are_ubo", "data_point/tests/provider/test_ubo.py::TestUBO::test_get_data_result_is_all_ubo_with_hits", "data_point/tests/provider/test_ubo.py::TestUBO::test_get_data_result_is_any_foreigner_ubo", "data_point/tests/provider/test_ubo.py::TestUBO::test_get_data_result_is_any_provided_shareholders_are_ubo", "data_point/tests/provider/test_ubo.py::TestUBO::test_get_data_result_is_any_ubo_with_hits", "data_point/tests/provider/test_ubo.py::TestUBO::test_get_data_result_is_any_us_citizen_ubo", "data_point/tests/provider/test_ubo.py::TestUBO::test_get_data_result_number_of_ubo", "data_point/tests/provider/test_ubo.py::TestUBO::test_request_ca_result_and_total_hits", "data_point/tests/provider/test_value_options.py::TestValueOptions::test_get_data_result[APPLICATION_STATUS-test_case_input1-expected_result1]", "data_point/tests/provider/test_value_options.py::TestValueOptions::test_get_data_result[CUSTOM_STATUS-test_case_input0-expected_result0]", "data_point/tests/report/test_business_report.py::TestBusinessReport::test_append_ubo_report_success", "data_point/tests/report/test_business_report.py::TestBusinessReport::test_get_ubo_report_success", "data_point/tests/report/test_comply_advantage_report.py::TestComplyAdvantageReport::test_get_ubo_aml_report", "data_point/tests/report/test_indentity_report.py::TestIndentityReport::test_get_dopa_report", "data_point/tests/report/test_led_report.py::TestLedReport::test_get_led_report", "data_point/tests/services/test_asia_verify_service.py::TestAsiaVerifyService::test_asia_verify_api_service[FAILED_VALIDATE: 404 not found.-test_case_input3-expected_result3]", "data_point/tests/services/test_asia_verify_service.py::TestAsiaVerifyService::test_asia_verify_api_service[FAILED_VALIDATE: handle no data found in response-test_case_input4-expected_result4]", "data_point/tests/services/test_asia_verify_service.py::TestAsiaVerifyService::test_asia_verify_api_service[SUCCESS_VALIDATE: Japan-test_case_input0-expected_result0]", "data_point/tests/services/test_asia_verify_service.py::TestAsiaVerifyService::test_asia_verify_api_service[SUCCESS_VALIDATE: Malaysia-test_case_input2-expected_result2]", "data_point/tests/services/test_asia_verify_service.py::TestAsiaVerifyService::test_asia_verify_api_service[SUCCESS_VALIDATE: Singapore-test_case_input1-expected_result1]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_birthdate[FAILED_VALIDATE: TEST_BIRTHDATE: Date is current date-test_case_input5-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_birthdate[FAILED_VALIDATE: TEST_BIRTHDATE: Date is future-test_case_input4-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_birthdate[FAILED_VALIDATE: TEST_BIRTHDATE: Empty value-test_case_input2-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_birthdate[FAILED_VALIDATE: TEST_BIRTHDATE: None value-test_case_input1-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_birthdate[FAILED_VALIDATE: TEST_BIRTHDATE: Not a date pattern-test_case_input3-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_birthdate[SUCCESS_VALIDATE: TEST_BIRTHDATE-test_case_input0-True]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_thai_id[FAILED_VALIDATE: TEST_THAI_ID: Contain value-test_case_input6-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_thai_id[FAILED_VALIDATE: TEST_THAI_ID: Empty value-test_case_input3-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_thai_id[FAILED_VALIDATE: TEST_THAI_ID: Invalid checksum-test_case_input1-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_thai_id[FAILED_VALIDATE: TEST_THAI_ID: Less 13 digits-test_case_input4-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_thai_id[FAILED_VALIDATE: TEST_THAI_ID: More 13 digits-test_case_input5-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_thai_id[FAILED_VALIDATE: TEST_THAI_ID: None value-test_case_input2-False]", "data_point/tests/test_utils.py::TestInputValidationUtil::test_thai_id[SUCCESS_VALIDATE: TEST_THAI_ID-test_case_input0-True]", "data_point/tests/utils/test_prefix_removal.py::TestPrefixRemoval::test_clean_in_prefix_removal", "decision_flow/tests/compiler/test_compile.py::CompileTest::test_compile_simple", "decision_flow/tests/compiler/test_compile.py::CompileTest::test_compile_simple_completed", "decision_flow/tests/compiler/test_compile.py::CompileTest::test_compile_simple_fail", "decision_flow/tests/compiler/test_compile_cache.py::CompileTest::test_compile_cache", "decision_flow/tests/compiler/test_error.py::CompileErrorTest::test_condition_error", "decision_flow/tests/converter/test_convert.py::ConvertTest::test_convert_fail", "decision_flow/tests/converter/test_convert.py::ConvertTest::test_convert_success", "decision_flow/tests/helper/test_execute_expression.py::ConvertTest::test_empty", "decision_flow/tests/helper/test_execute_expression.py::ConvertTest::test_ends_with", "decision_flow/tests/helper/test_execute_expression.py::ConvertTest::test_filled", "decision_flow/tests/helper/test_execute_expression.py::ConvertTest::test_has", "decision_flow/tests/helper/test_execute_expression.py::ConvertTest::test_not_has", "decision_flow/tests/helper/test_execute_expression.py::ConvertTest::test_starts_with", "decision_flow/tests/helper/test_get_relative_date.py::GetRelativeDateTest::test_with_invalid_extend_days", "decision_flow/tests/helper/test_get_relative_date.py::GetRelativeDateTest::test_with_valid_extend_days", "decision_flow/tests/helper/test_get_relative_date.py::GetRelativeDateTest::test_without_extend_days", "decision_flow/tests/helper/test_parse_value_type.py::ParseValueTypeTest::test_parse_boolean_type", "decision_flow/tests/helper/test_parse_value_type.py::ParseValueTypeTest::test_parse_date_type", "decision_flow/tests/helper/test_parse_value_type.py::ParseValueTypeTest::test_parse_dict_type", "decision_flow/tests/helper/test_parse_value_type.py::ParseValueTypeTest::test_parse_error", "decision_flow/tests/helper/test_parse_value_type.py::ParseValueTypeTest::test_parse_none_type", "decision_flow/tests/test_do_compute.py::ParseValueTypeTest::test_need_to_return_none_rendered_value_empty", "decision_flow/tests/test_do_compute.py::ParseValueTypeTest::test_need_to_return_value_of_relative_date_category", "decision_flow/tests/test_do_compute.py::ParseValueTypeTest::test_need_to_set_data_point", "decision_flow/tests/test_do_compute.py::ParseValueTypeTest::test_need_to_set_data_point_fail", "dynamicform/tests/helpers/test_escape.py::ValidatorExtensionTest::test_csv_injection_escape", "dynamicform/tests/schema/actions/test_mapping_params.py::MappingValueTest::test_can_mapping_newline", "dynamicform/tests/schema/actions/test_mapping_params.py::MappingValueTest::test_can_mapping_obj", "dynamicform/tests/schema/actions/test_mapping_params.py::MappingValueTest::test_can_mapping_params", "dynamicform/tests/schema/actions/test_mapping_params.py::MappingValueTest::test_return_empty_dict", "dynamicform/tests/schema/converter/test_collect_all_input.py::CollectAllInputsTest::test_collect_all_items", "dynamicform/tests/schema/converter/test_collect_all_input.py::CollectAllInputsTest::test_return_empty_json", "dynamicform/tests/schema/converter/test_collect_disable_logging.py::CollectDisableLoggingTest::test_collect_labels", "dynamicform/tests/schema/converter/test_collect_input_files.py::CollectInputsFilesTest::test_input_files", "dynamicform/tests/schema/converter/test_collect_label.py::CollectLabelsTest::test_collect_labels", "dynamicform/tests/schema/converter/test_collect_releted_questions.py::CollectReletedQuestionsTest::test_collect_releted_questions", "dynamicform/tests/schema/converter/test_collect_rule.py::CollectRulesTest::test_collect_rules", "dynamicform/tests/schema/converter/test_convert.py::ConvertTest::test_return_empty_json", "dynamicform/tests/schema/converter/test_convert.py::ConvertTest::test_something_works", "dynamicform/tests/schema/converter/test_convert_visible.py::ConvertTest::test_something_works", "dynamicform/tests/schema/converter/test_keep_answers_and_default.py::KeepAnswersAndDefaultTest::test_default_with_enabled_input_param", "dynamicform/tests/schema/converter/test_keep_answers_and_default.py::KeepAnswersAndDefaultTest::test_get_keep_answers_and_default", "dynamicform/tests/schema/converter/test_keep_answers_and_default.py::KeepAnswersAndDefaultTest::test_get_keep_answers_and_default_return_empty", "dynamicform/tests/schema/converter/test_validate_smart_uploader_payload.py::ValidateSmartUploaderPayloadTest::test_invalid_format_fields_name", "dynamicform/tests/schema/converter/test_validate_smart_uploader_payload.py::ValidateSmartUploaderPayloadTest::test_invalid_require_fields", "dynamicform/tests/schema/converter/test_validate_smart_uploader_payload.py::ValidateSmartUploaderPayloadTest::test_invalid_table_columns_name", "dynamicform/tests/schema/converter/test_validate_smart_uploader_payload.py::ValidateSmartUploaderPayloadTest::test_valid_empty_payload", "dynamicform/tests/schema/converter/test_validate_smart_uploader_payload.py::ValidateSmartUploaderPayloadTest::test_valid_payload", "dynamicform/tests/schema/getapp/test_get_app.py::ConvertTest::test_can_get_app_name", "dynamicform/tests/schema/lang/test_collect_text.py::CollectTextTest::test_can_collect_texts", "dynamicform/tests/schema/lang/test_form_locale.py::FormLocaleTest::test_can_translate_frontend_schema", "dynamicform/tests/schema/lang/test_trans.py::TransTextTest::test_can_translate_texts_enum", "dynamicform/tests/schema/rule/test_disable_rule.py::DisableRuleTest::test_disable_rules", "dynamicform/tests/schema/step/test_get_step.py::GetStepTest::test_can_get_step", "dynamicform/tests/schema/step/test_get_step.py::GetStepTest::test_get_step_default", "dynamicform/tests/schema/step/test_get_step.py::GetStepTest::test_get_step_default_if_not_found", "dynamicform/tests/schema/step/test_modify_validation_rules.py::ModifyValidationRulesTest::test_has_rules", "dynamicform/tests/schema/step/test_modify_validation_rules.py::ModifyValidationRulesTest::test_many_question", "dynamicform/tests/schema/step/test_modify_validation_rules.py::ModifyValidationRulesTest::test_no_rules", "dynamicform/tests/schema/step/test_modify_validation_rules.py::ModifyValidationRulesTest::test_schema_action_hide_step", "dynamicform/tests/schema/test_build.py::BuildTest::test_frontend_schema", "dynamicform/tests/schema/test_build.py::BuildTest::test_prefill_url", "dynamicform/tests/submodules/answer/test_log.py::SaveTest::test_can_save_and_log_update", "dynamicform/tests/submodules/answer/test_log.py::SaveTest::test_can_save_not_log", "dynamicform/tests/submodules/answer/test_log.py::SaveTest::test_can_save_with_answers", "dynamicform/tests/submodules/answerfile/test_delete.py::DeleteTest::test_can_delete_file", "dynamicform/tests/submodules/answerfile/test_delete.py::DeleteTest::test_cannot_delete_after_submit_form", "dynamicform/tests/submodules/answerfile/test_preview.py::PreviewTest::test_can_preview_file", "dynamicform/tests/submodules/answerfile/test_upload_file.py::SaveTest::test_can_upload_file_save_multiple_false", "dynamicform/tests/submodules/answerfile/test_upload_file.py::SaveTest::test_can_upload_multiple_files", "dynamicform/tests/submodules/answerfile/test_upload_file.py::SaveTest::test_can_upload_one_file", "dynamicform/tests/submodules/answerfile/test_upload_file.py::SaveTest::test_cannot_update_after_submit_form", "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_can_applied_form", "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_can_applied_form_with_answers", "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_can_applied_form_with_miss_required_answers", "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_can_applied_form_with_recaptcha", "dynamicform/tests/submodules/application/test_create.py::ApplyFormTest::test_cannot_applied_form_with_fail_answers", "dynamicform/tests/submodules/appliedform/test_get_answer.py::SaveGetAnswer::test_can_get_answers", "dynamicform/tests/submodules/appliedform/test_info.py::InfoTest::test_can_view_info", "dynamicform/tests/submodules/appliedform/test_info.py::InfoTest::test_multi_info", "dynamicform/tests/submodules/appliedform/test_optimize.py::OptimizeTest::test_can_save_with_answers", "dynamicform/tests/submodules/appliedform/test_save.py::SaveTest::test_can_save_with_answers", "dynamicform/tests/submodules/appliedform/test_save.py::SaveTest::test_can_save_with_answers_validation", "dynamicform/tests/submodules/appliedform/test_save.py::SaveTest::test_cannot_overide_disable_answers", "dynamicform/tests/submodules/appliedform/test_save.py::SaveTest::test_cannot_save_disable_answers", "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Already has full address case-test_case_input5-expected_result5]", "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Multiple address case-test_case_input3-expected_result3]", "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[No country case-test_case_input4-expected_result4]", "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Other country case-test_case_input2-expected_result2]", "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Thailand, Bangkok case-test_case_input0-expected_result0]", "dynamicform/tests/submodules/appliedform/test_save.py::TestSaveAddress::test_address_full_from_address_element[Thailand, other case-test_case_input1-expected_result1]", "dynamicform/tests/submodules/appliedform/test_save_app_level.py::SaveTest::test_can_save_with_answers", "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_can_create_and_save_encrypt_answers", "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_can_save_encrypt_answers", "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_can_save_encrypt_partition_answers", "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_can_save_with_answers_validation", "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_cannot_overide_disable_answers", "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_cannot_save_disable_answers", "dynamicform/tests/submodules/appliedform/test_save_encrypt.py::SaveTest::test_encrypt_and_decrypt_for_report", "dynamicform/tests/submodules/appliedform/test_save_with_file.py::SaveTest::test_can_save_with_answers", "dynamicform/tests/submodules/appliedform/test_save_with_file.py::SaveTest::test_cannot_submit_with_answers_validation", "dynamicform/tests/submodules/appliedform/test_smart_uploader.py::SmartUploaderGetAnswerToSaveTest::test_smart_uploader_get_answers_to_save", "dynamicform/tests/submodules/appliedform/test_smart_uploader_api.py::SmartUploaderAPITests::test_get_get_smartuploader_result_api", "dynamicform/tests/submodules/appliedform/test_smart_uploader_api.py::SmartUploaderAPITests::test_upload_file_api", "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_can_submit_with_answers", "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_can_submit_with_no_answers", "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_can_update_after_submit", "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_cannot_re_submit", "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_cannot_submit_with_answers_validation", "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_cannot_submit_with_never_answers", "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_cannot_update_to_submitted_form", "dynamicform/tests/submodules/appliedform/test_submit.py::SubmitTest::test_submit_with_consent", "dynamicform/tests/submodules/appliedform/test_submit_app_level.py::SubmitAppTest::test_submit_with_app_validation", "dynamicform/tests/submodules/appliedform/test_update_answers.py::UpdateTest::test_can_save_with_answers", "dynamicform/tests/submodules/decisionflow/models/test_on_other_event_failed.py::DecisionFlowOnOtherEventFailTest::test_not_trigger_on_other_event_fail", "dynamicform/tests/submodules/decisionflow/models/test_on_other_event_failed.py::DecisionFlowOnOtherEventFailTest::test_trigger_on_other_event_fail", "dynamicform/tests/submodules/decisionflow/views/test_decision_flow.py::ViewDecisionFlowTest::test_rerun_all_tasks", "dynamicform/tests/submodules/decisionflow/views/test_decision_flow.py::ViewDecisionFlowTest::test_rerun_selected_tasks", "dynamicform/tests/submodules/form/functions/test_assign_user.py::AssignUserTest::test_auto_assign_user_after_create_form", "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_can_update_if_applied_form_is_available", "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_cannot_update_form_inactive", "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_cannot_update_is_disabled", "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_cannot_update_is_drop_off", "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_cannot_update_is_submitted", "dynamicform/tests/submodules/form/functions/test_can_do_update.py::FormCanDoUpdateTest::test_my_form_can_update", "dynamicform/tests/submodules/form/functions/test_change_answer_key.py::ChangeAnswerKeyTest::test_normal_case", "dynamicform/tests/submodules/form/functions/test_get_dashboard.py::GetDashBoardColumnSettingTest::test_get_dashboard_deleted_status", "dynamicform/tests/submodules/form/functions/test_get_dashboard.py::GetDashBoardColumnSettingTest::test_get_dashboard_no_custom_status", "dynamicform/tests/submodules/form/functions/test_get_dashboard.py::GetDashBoardColumnSettingTest::test_get_dashboard_with_custom_status", "dynamicform/tests/submodules/form/functions/test_get_dashboard.py::GetDashBoardColumnSettingTest::test_get_dashboard_with_existing_custom_status", "dynamicform/tests/submodules/form/mixins/test_clone.py::FormCloneTests::test_clone", "dynamicform/tests/submodules/form/mixins/test_clone.py::FormCloneTests::test_clone_decision_flow_schema", "dynamicform/tests/submodules/form/mixins/test_clone.py::FormCloneTests::test_clone_with_api", "dynamicform/tests/submodules/form/test_encrypt_settings.py::EncryptSettingsTest::test_can_encrypt_settings", "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form", "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_and_submit_with_answers", "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_answers", "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_answers_old_version_api", "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_default_answers", "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_miss_required_answers", "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_can_applied_form_with_recaptcha", "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_cannot_applied_form_and_submit_with_invalid_answers", "dynamicform/tests/submodules/form/views/test_apply_form.py::ApplyFormTest::test_cannot_applied_form_with_fail_answers", "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_can_clone_with_ekyc", "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_can_clone_with_no_ekyc", "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_can_create_with_base64", "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_can_create_with_base64_portrait", "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_cannot_clone_form_another_workspace", "dynamicform/tests/submodules/form/views/test_create_form.py::CreateFormTest::test_can_create_form", "dynamicform/tests/submodules/form/views/test_create_form.py::CreateFormTest::test_cannot_create_form_invalid_json", "dynamicform/tests/submodules/form/views/test_create_form.py::CreateFormTest::test_cannot_create_form_missing_key_schema", "dynamicform/tests/submodules/form/views/test_create_form.py::CreateFormTest::test_cannot_create_form_with_forbidden_name", "dynamicform/tests/submodules/form/views/test_delete_custom_status.py::DeleteCustomStatus::test_api_can_delete_custom_status", "dynamicform/tests/submodules/form/views/test_delete_custom_status.py::DeleteCustomStatus::test_api_cannot_delete_custom_status", "dynamicform/tests/submodules/form/views/test_delete_custom_status.py::DeleteCustomStatus::test_api_cannot_delete_custom_status_from_data_point", "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_error_invalid_files", "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_error_unable_to_access_image", "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_error_unsupported_extension", "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_error_upload", "dynamicform/tests/submodules/form/views/test_public_uploader.py::PublicUploaderTest::test_upload_by_url_success", "dynamicform/tests/submodules/form/views/test_save_dashboard_settings.py::SaveDashboardSettingsTest::test_save_dashboard_settings_with_label", "dynamicform/tests/submodules/form/views/test_save_dashboard_settings.py::SaveDashboardSettingsTest::test_save_dashboard_settings_with_no_label", "dynamicform/tests/submodules/form/views/test_task.py::TaskViewTest::test_can_retrieve_task", "dynamicform/tests/submodules/form/views/test_update_form.py::UpdateFormTest::test_auto_build_backend_schema_after_update_form", "dynamicform/tests/submodules/form/views/test_update_form.py::UpdateFormTest::test_can_update_form", "dynamicform/tests/submodules/form/views/test_update_form.py::UpdateFormTest::test_cannot_update_form_invalid_json", "dynamicform/tests/submodules/form/views/test_update_form.py::UpdateFormTest::test_cannot_update_form_missing_key_schema", "dynamicform/tests/submodules/form/views/test_update_item.py::UpdateItemTest::test_update_smartuploader_item", "dynamicform/tests/submodules/form/views/test_update_report.py::UpdateFormTest::test_can_update_report", "dynamicform/tests/submodules/form/views/test_update_report.py::UpdateFormTest::test_cannot_update_form_invalid_json", "dynamicform/tests/submodules/page/test_view_permission.py::TestPageAPIPermisiionAPI::test_get_list_no_auth", "dynamicform/tests/submodules/page/test_view_permission.py::TestPageAPIPermisiionAPI::test_get_list_none_super_user", "dynamicform/tests/submodules/page/test_view_permission.py::TestPageAPIPermisiionAPI::test_get_list_super_user", "dynamicform/tests/submodules/speechtotext/test_convert.py::SaveTest::test_can_convert_audio", "dynamicform/tests/templatetags/test_templatetag.py::TemplatetagsTest::test_age", "dynamicform/tests/templatetags/test_templatetag.py::TemplatetagsTest::test_b64encode", "dynamicform/tests/templatetags/test_templatetag.py::TemplatetagsTest::test_bc_year", "dynamicform/tests/templatetags/test_templatetag.py::TemplatetagsTest::test_html_unescape", "dynamicform/tests/templatetags/test_templatetag.py::TemplatetagsTest::test_intcomma", "dynamicform/tests/templatetags/test_templatetag.py::TemplatetagsTest::test_json", "dynamicform/tests/templatetags/test_templatetag.py::TemplatetagsTest::test_relativedelta", "dynamicform/tests/templatetags/test_templatetag.py::TemplatetagsTest::test_strptime", "dynamicform/tests/test_face_compare_integration.py::FaceCompareIntegrationTests::test_appliedform_properties_ekyc_result", "dynamicform/tests/test_face_compare_integration.py::FaceCompareIntegrationTests::test_ekyc_update_application_status_with_custom_criteria", "dynamicform/tests/test_face_compare_integration.py::FaceCompareIntegrationTests::test_ekyc_update_application_status_with_default_criteria", "dynamicform/tests/test_face_compare_integration.py::FaceCompareIntegrationTests::test_update_face_compare_message", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_ekyc_update_application_status_with_custom_criteria", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_ekyc_update_application_status_with_custom_criteria_full_object", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_ekyc_update_application_status_with_default_criteria", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_ekyc_update_application_status_with_simple_custom_criteria", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_get_face_compare_criteria_fallback_to_env_then_default", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_get_face_compare_criteria_with_custom_answer", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_get_face_compare_criteria_with_custom_answer_full_object", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_get_face_compare_criteria_with_invalid_custom_answer", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_get_face_compare_criteria_with_missing_criteria_type", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_get_face_compare_criteria_with_missing_criteria_type_in_full_custom_answer", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_get_face_compare_criteria_with_no_custom_answer", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_get_face_compare_criteria_with_numeric_value_as_custom_answer", "dynamicform/tests/test_face_compare_real_world.py::FaceCompareRealWorldTests::test_get_face_compare_criteria_with_simple_custom_answer", "dynamicform/tests/test_hook.py::GetFaceCompareCriteriaTests::test_get_face_compare_criteria_with_custom_answer", "dynamicform/tests/test_hook.py::GetFaceCompareCriteriaTests::test_get_face_compare_criteria_with_exception_during_processing", "dynamicform/tests/test_hook.py::GetFaceCompareCriteriaTests::test_get_face_compare_criteria_with_invalid_custom_answer", "dynamicform/tests/test_hook.py::GetFaceCompareCriteriaTests::test_get_face_compare_criteria_with_missing_criteria_type", "dynamicform/tests/test_hook.py::GetFaceCompareCriteriaTests::test_get_face_compare_criteria_with_no_custom_answer", "dynamicform/tests/test_hook.py::GetStatusFromCriteriaTests::test_get_status_from_criteria_with_custom_criteria", "dynamicform/tests/test_hook.py::GetStatusFromCriteriaTests::test_get_status_from_criteria_with_default", "dynamicform/tests/test_hook.py::GetStatusFromCriteriaTests::test_get_status_from_criteria_with_none_score", "dynamicform/tests/validate/test_extension.py::ValidatorExtensionTest::test_validate_not_pass", "dynamicform/tests/validate/test_extension.py::ValidatorExtensionTest::test_validate_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_between_number_rules_not_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_between_number_rules_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_business_id_fail", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_business_id_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_datetime_after_rule_fail", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_datetime_after_rule_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_datetime_before_rule_fail", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_datetime_before_rule_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_different_not_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_different_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_email", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_is_rule_not_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_is_rule_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_not_in_rule_not_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_not_in_rule_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_numeric_not_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_numeric_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_pass_checksum_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_regex_rules_not_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_regex_rules_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_required_if_condition_not_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_required_if_condition_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_required_with_all_equal_value_rule_not_pass", "dynamicform/tests/validate/test_validator.py::ValidatorTest::test_validate_required_with_all_equal_value_rule_pass", "ekyc/tests/test_api_gateway_header_body.py::TestBaseDocument::test_get_additional_body", "ekyc/tests/test_api_gateway_header_body.py::TestBaseDocument::test_get_additional_body_auto_detect", "ekyc/tests/test_api_gateway_header_body.py::TestBaseDocument::test_submit_additional_header_settings", "ekyc/tests/test_back.py::BackCardTests::test_api_back_fail_no_file", "ekyc/tests/test_back.py::BackCardTests::test_api_back_max_attempt", "ekyc/tests/test_back.py::BackCardTests::test_api_back_nonce", "ekyc/tests/test_back.py::BackCardTests::test_api_back_pass", "ekyc/tests/test_back.py::BackCardTests::test_api_back_webhook", "ekyc/tests/test_back.py::BackCardTests::test_api_report", "ekyc/tests/test_back.py::BackCardTests::test_api_report_on_fail_attempt", "ekyc/tests/test_back.py::BackPicTests::test_api_back_fail_no_file", "ekyc/tests/test_back.py::BackPicTests::test_api_back_max_attempt", "ekyc/tests/test_back.py::BackPicTests::test_api_back_nonce", "ekyc/tests/test_back.py::BackPicTests::test_api_back_pass", "ekyc/tests/test_back.py::BackPicTests::test_api_back_webhook", "ekyc/tests/test_blacklist_key.py::BlacklistKeyLivenessTests::test_liveness_upload_includes_blacklist_key", "ekyc/tests/test_blacklist_key.py::BlacklistKeyLivenessTests::test_liveness_upload_without_blacklist_key", "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_document_upload_includes_blacklist_key", "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_document_upload_without_blacklist_key", "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_get_additional_header_includes_blacklist_key", "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_get_additional_header_without_blacklist_key", "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_liveness_upload_includes_blacklist_key", "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_liveness_upload_without_blacklist_key", "ekyc/tests/test_blacklist_key.py::GetAdditionalHeaderTests::test_get_additional_header_includes_blacklist_key", "ekyc/tests/test_blacklist_key.py::GetAdditionalHeaderTests::test_get_additional_header_with_x_forwarded_for", "ekyc/tests/test_blacklist_key.py::GetAdditionalHeaderTests::test_get_additional_header_without_blacklist_key", "ekyc/tests/test_checker_expiry.py::EkycCheckerExpiryTests::test_expiry_fail", "ekyc/tests/test_checker_expiry.py::EkycCheckerExpiryTests::test_expiry_ignore_not_found", "ekyc/tests/test_checker_expiry.py::EkycCheckerExpiryTests::test_expiry_lifelong", "ekyc/tests/test_checker_expiry.py::EkycCheckerExpiryTests::test_expiry_not_found", "ekyc/tests/test_checker_expiry.py::EkycCheckerExpiryTests::test_expiry_pass", "ekyc/tests/test_checker_expiry.py::EkycCheckerExpiryTests::test_not_check_expiry", "ekyc/tests/test_checker_expiry.py::test_expiry_check_disabled", "ekyc/tests/test_checker_expiry.py::test_expiry_check_expired_document", "ekyc/tests/test_checker_expiry.py::test_expiry_check_lifelong_document", "ekyc/tests/test_checker_expiry.py::test_expiry_check_valid_document", "ekyc/tests/test_checker_expiry.py::test_expiry_condition_excluded_document_types", "ekyc/tests/test_checker_expiry.py::test_expiry_not_found", "ekyc/tests/test_checker_expiry.py::test_expiry_not_found_ignored", "ekyc/tests/test_faceactions.py::EkycTests::test_liveness_faceaction_random_2", "ekyc/tests/test_faceactions.py::EkycTests::test_liveness_faceaction_random_4", "ekyc/tests/test_faceactions.py::EkycTests::test_payload", "ekyc/tests/test_form.py::EkycTests::test_document_build", "ekyc/tests/test_form.py::EkycTests::test_front_card_build", "ekyc/tests/test_form.py::EkycTests::test_get_answer", "ekyc/tests/test_form.py::EkycTests::test_get_extra", "ekyc/tests/test_form.py::EkycTests::test_get_extra_encrypt_url", "ekyc/tests/test_form.py::EkycTests::test_get_report", "ekyc/tests/test_form.py::EkycTests::test_get_report_preview_url", "ekyc/tests/test_form.py::EkycTests::test_liveness_build", "ekyc/tests/test_form.py::EkycTests::test_liveness_faceaction_pool_list", "ekyc/tests/test_form_back.py::EkycTests::test_back_card_check_warning_all", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_image_quality_fail", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_image_quality_none", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_image_quality_pass", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_liveness_detection_fail", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_liveness_detection_none", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_liveness_detection_pass", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_mrz_expiry_fail", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_mrz_expiry_none", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_mrz_expiry_pass", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_mrz_fail", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_mrz_none", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_ignore_mrz_pass", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_image_quality_none", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_liveness_detection_none", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_mrz_expiry_fail", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_mrz_expiry_none", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_mrz_expiry_pass", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_mrz_fail", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_mrz_none", "ekyc/tests/test_form_ci_passport.py::EkycTests::test_ci_passport_mrz_pass", "ekyc/tests/test_form_front.py::EkycTests::test_auto_detect_document_type", "ekyc/tests/test_form_front.py::EkycTests::test_front_card_check_age", "ekyc/tests/test_form_front.py::EkycTests::test_front_card_check_expiry", "ekyc/tests/test_form_front.py::EkycTests::test_front_card_check_hashed_document_match_with", "ekyc/tests/test_form_front.py::EkycTests::test_front_card_check_id_match_with", "ekyc/tests/test_form_front.py::EkycTests::test_front_card_check_ocr_fields", "ekyc/tests/test_form_front.py::EkycTests::test_front_card_check_warning_all", "ekyc/tests/test_form_front.py::EkycTests::test_passport_mrz", "ekyc/tests/test_form_nfc.py::DocumentTests::test_other_document_should_not_check_nfc", "ekyc/tests/test_form_nfc.py::EkycTests::test_nfc_fail", "ekyc/tests/test_form_nfc.py::EkycTests::test_nfc_no_document", "ekyc/tests/test_form_nfc.py::EkycTests::test_nfc_none", "ekyc/tests/test_form_nfc.py::EkycTests::test_nfc_pass", "ekyc/tests/test_form_nfc.py::EkycTests::test_other_document_should_not_check_nfc", "ekyc/tests/test_form_nfc.py::EkycTests::test_passport_nfc_fail", "ekyc/tests/test_form_nfc.py::EkycTests::test_passport_nfc_no_document", "ekyc/tests/test_form_nfc.py::EkycTests::test_passport_nfc_none", "ekyc/tests/test_form_nfc.py::EkycTests::test_passport_nfc_pass", "ekyc/tests/test_form_nfc.py::NfcTests::test_get_nfc_token_fail", "ekyc/tests/test_form_nfc.py::NfcTests::test_get_nfc_token_success", "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_fail", "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_no_document", "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_none", "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_pass", "ekyc/tests/test_form_nfc.py::PassportTests::test_nfc_fail", "ekyc/tests/test_form_nfc.py::PassportTests::test_nfc_no_document", "ekyc/tests/test_form_nfc.py::PassportTests::test_nfc_none", "ekyc/tests/test_form_nfc.py::PassportTests::test_nfc_pass", "ekyc/tests/test_form_passport.py::EkycTests::test_passport", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_id_live_detection_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_id_live_detection_fail", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_id_live_detection_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_id_live_detection_pass", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_image_quality_fail", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_image_quality_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_image_quality_pass", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_liveness_detection_fail", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_liveness_detection_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_liveness_detection_pass", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_mrz_expiry_fail", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_mrz_expiry_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_mrz_expiry_pass", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_mrz_fail", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_mrz_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_ignore_mrz_pass", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_image_quality_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_liveness_detection_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_mrz_expiry_fail", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_mrz_expiry_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_mrz_expiry_pass", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_mrz_fail", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_mrz_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_mrz_pass", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_nfc", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_nfc_none", "ekyc/tests/test_form_passport.py::EkycTests::test_passport_nfc_pass", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_image_quality_fail", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_image_quality_none", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_image_quality_pass", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_liveness_detection_fail", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_liveness_detection_none", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_liveness_detection_pass", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_mrz_expiry_fail", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_mrz_expiry_none", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_mrz_expiry_pass", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_mrz_fail", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_mrz_none", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_ignore_mrz_pass", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_image_quality_none", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_liveness_detection_none", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_mrz_expiry_fail", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_mrz_expiry_none", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_mrz_expiry_pass", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_mrz_fail", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_mrz_none", "ekyc/tests/test_form_travel_document.py::EkycTests::test_travel_document_mrz_pass", "ekyc/tests/test_front.py::DocumentTests::test_api_doc_fail_no_file", "ekyc/tests/test_front.py::DocumentTests::test_api_doc_max_attempt", "ekyc/tests/test_front.py::DocumentTests::test_api_doc_nonce", "ekyc/tests/test_front.py::DocumentTests::test_api_doc_pass_all", "ekyc/tests/test_front.py::DocumentTests::test_api_doc_pass_face_only", "ekyc/tests/test_front.py::DocumentTests::test_api_doc_pass_nothing", "ekyc/tests/test_front.py::DocumentTests::test_api_doc_too_many_files", "ekyc/tests/test_front.py::DocumentTests::test_api_doc_webhook", "ekyc/tests/test_front.py::DocumentTests::test_api_get_result", "ekyc/tests/test_front.py::DocumentTests::test_get_report_warning", "ekyc/tests/test_front.py::DriverLicenseTests::test_api_doc_fail_no_file", "ekyc/tests/test_front.py::DriverLicenseTests::test_api_doc_max_attempt", "ekyc/tests/test_front.py::DriverLicenseTests::test_api_doc_nonce", "ekyc/tests/test_front.py::DriverLicenseTests::test_api_doc_pass_all", "ekyc/tests/test_front.py::DriverLicenseTests::test_api_doc_pass_face_only", "ekyc/tests/test_front.py::DriverLicenseTests::test_api_doc_pass_nothing", "ekyc/tests/test_front.py::DriverLicenseTests::test_api_doc_too_many_files", "ekyc/tests/test_front.py::DriverLicenseTests::test_api_doc_webhook", "ekyc/tests/test_front.py::DriverLicenseTests::test_api_get_result", "ekyc/tests/test_front.py::DriverLicenseTests::test_get_report_warning", "ekyc/tests/test_front.py::FrontCardTests::test_api_doc_fail_no_file", "ekyc/tests/test_front.py::FrontCardTests::test_api_doc_max_attempt", "ekyc/tests/test_front.py::FrontCardTests::test_api_doc_nonce", "ekyc/tests/test_front.py::FrontCardTests::test_api_doc_pass_all", "ekyc/tests/test_front.py::FrontCardTests::test_api_doc_pass_face_only", "ekyc/tests/test_front.py::FrontCardTests::test_api_doc_pass_nothing", "ekyc/tests/test_front.py::FrontCardTests::test_api_doc_too_many_files", "ekyc/tests/test_front.py::FrontCardTests::test_api_doc_webhook", "ekyc/tests/test_front.py::FrontCardTests::test_api_get_result", "ekyc/tests/test_front.py::FrontCardTests::test_get_report_warning", "ekyc/tests/test_front.py::PassportTests::test_api_doc_fail_no_file", "ekyc/tests/test_front.py::PassportTests::test_api_doc_max_attempt", "ekyc/tests/test_front.py::PassportTests::test_api_doc_nonce", "ekyc/tests/test_front.py::PassportTests::test_api_doc_pass_all", "ekyc/tests/test_front.py::PassportTests::test_api_doc_pass_face_only", "ekyc/tests/test_front.py::PassportTests::test_api_doc_pass_nothing", "ekyc/tests/test_front.py::PassportTests::test_api_doc_too_many_files", "ekyc/tests/test_front.py::PassportTests::test_api_doc_webhook", "ekyc/tests/test_front.py::PassportTests::test_api_get_result", "ekyc/tests/test_front.py::PassportTests::test_get_report_warning", "ekyc/tests/test_front.py::ResidencePermitTests::test_api_doc_fail_no_file", "ekyc/tests/test_front.py::ResidencePermitTests::test_api_doc_max_attempt", "ekyc/tests/test_front.py::ResidencePermitTests::test_api_doc_nonce", "ekyc/tests/test_front.py::ResidencePermitTests::test_api_doc_pass_all", "ekyc/tests/test_front.py::ResidencePermitTests::test_api_doc_pass_face_only", "ekyc/tests/test_front.py::ResidencePermitTests::test_api_doc_pass_nothing", "ekyc/tests/test_front.py::ResidencePermitTests::test_api_doc_too_many_files", "ekyc/tests/test_front.py::ResidencePermitTests::test_api_doc_webhook", "ekyc/tests/test_front.py::ResidencePermitTests::test_api_get_result", "ekyc/tests/test_front.py::ResidencePermitTests::test_get_report_warning", "ekyc/tests/test_liveness.py::LivenessTests::test_api_hashing_error", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_face_not_found", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_files_not_equal_actions", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_last_attempt_racing", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_max_attempt", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_no_top_level_face_compare_shows_compare_url", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_pass_all", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_pass_but_backend_check_dont", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_saves_facecompare_when_top_level_face_compare_present", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_status_flow", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_webhook_called_once", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_webhook_ends_with_cancelled", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_webhook_ends_with_failed_backend", "ekyc/tests/test_liveness.py::LivenessTests::test_api_liveness_webhook_ends_with_failed_frontend", "ekyc/tests/test_liveness.py::LivenessTests::test_latest_success_flow", "ekyc/tests/test_liveness.py::LivenessTests::test_liveness_log", "ekyc/tests/test_max_attempt.py::EkycTests::test_max_attempt_by_env", "ekyc/tests/test_max_attempt.py::EkycTests::test_max_attempt_by_form_setting", "ekyc/tests/test_max_attempt.py::EkycTests::test_max_attempt_by_schema", "ekyc/tests/test_nfc.py::EkycTests::test_passport_nfc_none", "ekyc/tests/test_nfc.py::EkycTests::test_passport_nfc_pass", "ekyc/tests/test_non_blocking_requests.py::NonBlockingRequestsTest::test_get_extra_non_blocking", "ekyc/tests/test_non_blocking_requests.py::NonBlockingRequestsTest::test_multiple_requests_concurrency", "ekyc/tests/test_non_blocking_requests.py::NonBlockingRequestsTest::test_thread_pool_is_used", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_address", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_address_validation", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_age_date_incomplete", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_age_date_month_incomplete", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_alien_should_not_expiry", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_date_formatted", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_date_incomplete", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_date_no_dot", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_document_number", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_front_card_extractor", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_fullname_but_no_firstname_lastname", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_gender", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_name_en_only", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_name_th_only", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_name_type_criteria", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_title_but_no_firstname_lastname", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_title_from_passport_gender", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_title_from_passport_name", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_title_multiple", "ekyc/tests/test_ocr_extractor.py::EkycTests::test_title_should_not_replace", "ekyc/tests/test_preview_url.py::EkycTests::test_get_report_preview_url", "ekyc/tests/test_preview_url.py::EkycTests::test_get_report_preview_url_no_expire", "ekyc/tests/test_preview_url.py::EkycTests::test_get_report_preview_url_with_expire", "ekyc/tests/test_preview_url.py::EkycTests::test_get_report_preview_url_with_expire_case_api_timeout", "ekyc/tests/test_result.py::EkycResultTests::test_get_result", "ekyc/tests/test_result.py::EkycResultTests::test_result_flow", "ekyc/tests/test_result.py::EkycResultTests::test_result_flow_ensure_facecompare", "ekyc/tests/test_result.py::EkycResultTests::test_result_flow_normal", "ekyc/tests/test_sandbox.py::EkycSandboxTests::test_get_report", "ekyc/tests/test_sandbox.py::EkycSandboxTests::test_sandbox_get_answers", "ekyc/tests/test_sandbox.py::EkycSandboxTests::test_sandbox_get_answers_normal", "ekyc/tests/test_sandbox.py::EkycSandboxTests::test_sandbox_get_answers_submit", "ekyc/tests/test_sandbox.py::EkycSandboxTests::test_sandbox_pre_save", "ndid/tests/test_api.py::NdidTests::test_failed_flow", "ndid/tests/test_api.py::NdidTests::test_get_idps", "ndid/tests/test_api.py::NdidTests::test_success_flow", "otp/tests/test_request.py::OtpTests::test_can_change_mobile_number_with_same_ref", "otp/tests/test_request.py::OtpTests::test_can_change_mobile_number_with_same_ref_increasing_time", "otp/tests/test_request.py::OtpTests::test_can_request_otp", "otp/tests/test_request.py::OtpTests::test_cannot_request_otp_in_time_limit", "otp/tests/test_request.py::OtpTests::test_cannot_request_same_ref_over_max_request", "otp/tests/test_verify.py::OtpTests::test_can_verify_otp", "otp/tests/test_verify.py::OtpTests::test_can_verify_otp_with_ref_slug", "otp/tests/test_verify.py::OtpTests::test_cannot_verify_otp_expired", "otp/tests/test_verify.py::OtpTests::test_cannot_verify_otp_over_max_attempt", "payment_gateway/tests/test_2c2p.py::Vendor2C2PTest::test_is_valid_backend_response", "payment_gateway/tests/test_2c2p.py::Vendor2C2PTest::test_is_valid_backend_response_fail", "payment_gateway/tests/test_2c2p.py::Vendor2C2PTest::test_is_valid_frontend_response", "payment_gateway/tests/test_ttbqr.py::VendorTTBQRTest::test_is_valid_qrcode", "psychometric/tests/test_common.py::PsychometricTests::test_chunk_split", "psychometric/tests/test_form.py::PsychometricDynamicFormTests::test_answer_final_save", "psychometric/tests/test_form.py::PsychometricDynamicFormTests::test_info_hide_answered", "psychometric/tests/test_form.py::PsychometricDynamicFormTests::test_log_save", "psychometric/tests/test_get_submit_answers.py::GetSubmitAnswersTests::test_get_submit_answers", "services/datahubservices/tests/test_external.py::ExternalServicesTest::test_call_api", "services/tests/test_external.py::ExternalServicesTest::test_call_only_whitelist", "services/tests/test_external.py::ExternalServicesTest::test_not_call_outside_whitelist", "services/tests/test_get_ms_credential.py::GetMicroServiceCredentialTest::test_can_get_ms_credential_default", "services/tests/test_get_ms_credential.py::GetMicroServiceCredentialTest::test_can_get_ms_credential_with_form_slug", "services/tests/test_get_ms_credential.py::GetMicroServiceCredentialTest::test_can_get_ms_credential_with_target_host", "smartuploader/tests/test_callback.py::CallbackTest::test_callback_no_editor_permission", "smartuploader/tests/test_callback.py::CallbackTest::test_callback_not_in_workspace", "smartuploader/tests/test_callback.py::CallbackTest::test_callback_success", "smartuploader/tests/test_model.py::ModelTest::test_info_complete", "smartuploader/tests/test_model.py::ModelTest::test_info_failed", "smartuploader/tests/test_model.py::ModelTest::test_info_failed_processing", "smartuploader/tests/test_model.py::ModelTest::test_info_passed", "smartuploader/tests/test_model.py::ModelTest::test_info_passed_has_validation_failed", "smartuploader/tests/test_model.py::ModelTest::test_info_passed_has_validation_passed", "smartuploader/tests/test_model.py::ModelTest::test_info_passed_no_validation", "smartuploader/tests/test_model.py::ModelTest::test_info_processing", "smartuploader/tests/test_model.py::ModelTest::test_info_timeout", "smartuploader/tests/test_model.py::ModelTest::test_info_timeout_fail", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_called", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_correct_pages", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_free", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_free_has_file_should_NOT_deduct", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_free_no_file_should_NOT_deduct", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_paid_and_cant_deduct", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_paid_and_deducted", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_paid_but_no_file", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_paid_has_file_SHOULD_deduct", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_paid_has_file_but_CANT_deduct", "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_paid_no_file_should_NOT_deduct", "style/tests/test_api.py::TestStyleAPI::test_get_list_no_auth", "style/tests/test_api.py::TestStyleAPI::test_get_list_none_super_user", "style/tests/test_api.py::TestStyleAPI::test_get_list_super_user", "style/tests/test_api.py::TestStyleAPI::test_post_no_auth", "style/tests/test_api.py::TestStyleAPI::test_post_none_super_user", "style/tests/test_api.py::TestStyleAPI::test_post_super_user", "style/tests/test_style.py::TestValidateStyle::test_validate_style_with_error", "style/tests/test_style.py::TestValidateStyle::test_validate_style_with_ok", "webhook/tests/test_trigger.py::TriggerTest::test_encrypt", "webhook/tests/test_trigger.py::TriggerTest::test_trigger_custom_method_", "webhook/tests/test_webhook_old_version.py::WebhookTest::test_is_allow_webhook", "webhook/tests/test_webhook_old_version.py::WebhookTest::test_is_not_allow_webhook", "webhook/tests/test_webhook_old_version.py::WebhookTest::test_payload", "webhook/tests/test_webhook_old_version.py::WebhookTest::test_payload_dynamicform", "webhook/tests/test_webhook_old_version.py::WebhookTest::test_webhook", "workspace/tests/events/test_convert_event_decision_flow_key_to_webhook_key.py::ConvertEventDecisionFlowKeyToWebhookKey::test_event_in_alowed_events", "workspace/tests/events/test_convert_event_decision_flow_key_to_webhook_key.py::ConvertEventDecisionFlowKeyToWebhookKey::test_event_in_section", "workspace/tests/events/test_convert_event_decision_flow_key_to_webhook_key.py::ConvertEventDecisionFlowKeyToWebhookKey::test_no_event_in_any_list", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_can_and_log_change", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_can_check_can_deduct_credit", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_can_encode_member_token_and_decode_member_token", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_can_get_member_from_token", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_can_get_my_workspaces", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_can_invite_and_trigger_send_invite_email", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_create_checkout_fail_if_package_id_not_found", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_get_forms_limit", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_is_allow_member", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_is_obj_allow_member", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_manual_add_credit", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_obj_str", "workspace/tests/models/test_workspace.py::WorkspaceModelTests::test_user_can_ceate_a_workspace", "workspace/tests/test_checkout.py::TestCheckOutTestCase::test_checkout_can_create_transaction", "workspace/tests/test_checkout.py::TestCheckOutTestCase::test_checkout_response_error", "workspace/tests/test_checkout_auto_renew_callback.py::TestCheckOutTestCase::test_auto_renew_callback_charge_success", "workspace/tests/test_checkout_auto_renew_callback.py::TestCheckOutTestCase::test_auto_renew_callback_payment_intent_success", "workspace/tests/test_checkout_auto_renew_callback.py::TestCheckOutTestCase::test_auto_renew_payment", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_auto_renew_trigger", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_call_charge_success_then_checkout_session_completed", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_call_checkout_session_completed_then_charge_success", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_1_credit", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_3_credit", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_credit_for_calculate_alert_email", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_expired_flow_2_over_package", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_flow", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_flow_2_over_package", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_flow_over_1_package", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_no_credit", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_send_email", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_get_payment_intent_id_from_event", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_ignore_update_transaction_status", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_paid_package", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_charge_fail", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_charge_success", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_charge_update", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_checkout_session_complete", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_checkout_session_completed", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_payment_intent_created", "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_payment_intent_success", "workspace/tests/test_credit_usage_history.py::TestUpdateCreditUsage::test_auto_renew_callback_charge_success", "workspace/tests/test_email_notification.py::TestEmailNotification::test_send_empty_credit_warning_email", "workspace/tests/test_email_notification.py::TestEmailNotification::test_send_low_credit_warning_email", "workspace/tests/test_form_limit.py::FormLimitTests::test_can_create_form", "workspace/tests/test_form_limit.py::FormLimitTests::test_can_create_form_after_delete_a_form", "workspace/tests/test_form_limit.py::FormLimitTests::test_can_duplicatie_form", "workspace/tests/test_form_limit.py::FormLimitTests::test_can_duplicatie_form_after_delete_a_form", "workspace/tests/test_form_limit.py::FormLimitTests::test_cannot_create_form", "workspace/tests/test_form_limit.py::FormLimitTests::test_cannot_duplicatie_form", "workspace/tests/test_permissions.py::PermissionTests::test_api_account_setting", "workspace/tests/test_permissions.py::PermissionTests::test_api_add_new_application", "workspace/tests/test_permissions.py::PermissionTests::test_api_allow_only_superuser", "workspace/tests/test_permissions.py::PermissionTests::test_api_buy_credit", "workspace/tests/test_permissions.py::PermissionTests::test_api_connect_data_connection", "workspace/tests/test_permissions.py::PermissionTests::test_api_connect_integration", "workspace/tests/test_permissions.py::PermissionTests::test_api_connect_webhook", "workspace/tests/test_permissions.py::PermissionTests::test_api_create_edit_decision_flows", "workspace/tests/test_permissions.py::PermissionTests::test_api_create_edit_delete_api_token", "workspace/tests/test_permissions.py::PermissionTests::test_api_create_edit_duplicate_flow", "workspace/tests/test_permissions.py::PermissionTests::test_api_create_workspace", "workspace/tests/test_permissions.py::PermissionTests::test_api_delete_application", "workspace/tests/test_permissions.py::PermissionTests::test_api_delete_flow", "workspace/tests/test_permissions.py::PermissionTests::test_api_delete_workspace_flow", "workspace/tests/test_permissions.py::PermissionTests::test_api_edit_submission_detail", "workspace/tests/test_permissions.py::PermissionTests::test_api_edit_submission_detail_smartuploader", "workspace/tests/test_permissions.py::PermissionTests::test_api_edit_submission_status", "workspace/tests/test_permissions.py::PermissionTests::test_api_form_no_auth_user", "workspace/tests/test_permissions.py::PermissionTests::test_api_manage_users", "workspace/tests/test_permissions.py::PermissionTests::test_api_resend_webhook", "workspace/tests/test_permissions.py::PermissionTests::test_api_run_and_rerun_decision_flows", "workspace/tests/test_permissions.py::PermissionTests::test_api_set_styling_inflow", "workspace/tests/test_permissions.py::PermissionTests::test_api_system_background_tasks", "workspace/tests/test_permissions.py::PermissionTests::test_api_system_background_tasks_no_auth", "workspace/tests/test_permissions.py::PermissionTests::test_api_token_has_permission_on_form", "workspace/tests/test_permissions.py::PermissionTests::test_api_token_has_permission_on_workspace", "workspace/tests/test_permissions.py::PermissionTests::test_api_uppass_mobile_sdk", "workspace/tests/test_permissions.py::PermissionTests::test_api_view_data_connection", "workspace/tests/test_permissions.py::PermissionTests::test_api_view_ekyc_image", "workspace/tests/test_permissions.py::PermissionTests::test_api_view_ekyc_report_result", "workspace/tests/test_permissions.py::PermissionTests::test_api_view_submission_dashboard", "workspace/tests/test_permissions.py::PermissionTests::test_api_view_submission_detail", "workspace/tests/test_permissions.py::PermissionTests::test_has_permission_on_form", "workspace/tests/views/test_token.py::TokenViewTests::test_an_admin_can_create_token", "workspace/tests/views/test_token.py::TokenViewTests::test_an_admin_can_update_token", "workspace/tests/views/test_token.py::TokenViewTests::test_an_admin_cannot_create_token"]