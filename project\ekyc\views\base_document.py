import json
from datetime import datetime
from typing import Literal
from rest_framework import status
from rest_framework.response import Response
from rest_framework.reverse import reverse
from rest_framework.decorators import action
from pydash import get, set_, pick
from django.conf import settings

from project.custom_logger import logger
from ..helpers.log import send_log
from ..helpers.utils import get_additional_header
from ..helpers.sjcl_encryption import decrypt_header
from ..helpers.set_transaction import do_ensure_facecompare, set_document
from ..helpers.ocr_extractor import extract_document
from ..helpers.get_form import get_applied_form, get_form_or_document_item, get_form_answer, get_form_settings
from ..helpers.document_checker import check_by_schema_conditions
from ..helpers.document_autofill import autofill_by_schema
from ..helpers.sjcl_encryption import encrypt_url
from ..helpers.credit import raise_if_cant_deduct_credit_document
from ..models import Ekyc
from .base_ekyc import BaseEkycViewset
from ..serializers import EncryptedSerializer, CheckQualitySerializer, CardSubmitSerializer

EKYC_RESULT_IMAGE_TIMEOUT = settings.EKYC_RESULT_IMAGE_TIMEOUT


def get_additional_body(
    serializer: CardSubmitSerializer, form, applied_form, model_name: str, document_type: str, schema: dict
):
    features = get_form_settings(form, f"ekyc.{model_name}.features", {})
    orientation = serializer.data.get("orientation")
    logs = serializer.data.get("logs")
    card_rect = get(logs, [0, "card_rect"], None)
    accepted_document_types_data = serializer.data.get("accepted_document_types", None)
    accepted_document_types = None

    accepted_countries = serializer.data.get("accepted_countries")
    if not accepted_countries:
        accepted_countries = get(schema, ["accepted_countries", document_type], [])

    accepted_countries_per_document = {}
    if document_type == "auto":
        # Process accepted_document_types
        if accepted_document_types_data and len(accepted_document_types_data) > 0:
            accepted_document_types = ",".join(accepted_document_types_data)

        # Process accepted_countries_per_document
        all_accepted_countries: dict = get(schema, ["accepted_countries"], {})
        for key, value in all_accepted_countries.items():
            if not value or len(value) == 0:
                from dynamicform.submodules.appliedform.dynamicform.save import get_country_info_dict

                country_info_dict = get_country_info_dict()
                value = list(country_info_dict.keys())

            accepted_countries_per_document[f"countries_{key}"] = ",".join(value)

    auto_detect_country = get(schema, ["auto_detect_country", document_type], False)
    if auto_detect_country and not accepted_countries:
        from dynamicform.submodules.appliedform.dynamicform.save import get_country_info_dict

        country_info_dict = get_country_info_dict()
        accepted_countries = list(country_info_dict.keys())

    # Get additional_body from formsettings
    additional_body_settings: dict[str, str] = get_form_settings(form, "ekyc.front_card.additional_body", {})

    # Get all answers_to_upload
    answers_to_upload_map: dict[str, str] = get_form_settings(form, "ekyc.front_card.answers_to_upload", {})
    answers_to_upload = {}
    if answers_to_upload_map and applied_form:
        for dest, src in answers_to_upload_map.items():
            answer = get_form_answer(applied_form, src)
            if not isinstance(answer, str):
                answer = json.dumps(answer)
            answers_to_upload[dest] = answer

    return {
        "countries": json.dumps(accepted_countries),
        "features": json.dumps(features),
        "orientation": orientation,
        "card_rect": json.dumps(card_rect),
        "accepted_document_types": accepted_document_types,
        **accepted_countries_per_document,
        **additional_body_settings,
        **answers_to_upload,
    }


class BaseDocumentViewSet(BaseEkycViewset):
    parent_lookup_kwargs = {"ekyc": "ekyc"}
    model_name: Literal["front_card", "back_card"] = "front_card"
    document_type = "front_card"
    log_name = "frontcard"
    field_name = "FrontCard"

    def call_uploader(self, *args, **kwargs):
        print("NO UPLOAD FUNCTION")

    def call_check_quality(self, *args, **kwargs):
        print("NO CHECK QUALITY FUNCTION")

    @action(detail=False, methods=["post"])
    def submit(self, request, ekyc_ref, **kwargs):
        process_logs = [["backend_start", str(datetime.now().isoformat())]]

        applied_form = get_applied_form(ekyc_ref)

        def __send_info_log(is_fail=False):
            if applied_form:
                action = f"info_backend_submit_{self.log_name}"
                to_log = {
                    "media": "document",
                    "code": action,
                    "error": process_logs,
                    "log_type": "info",
                }
                applied_form.log(action=action, detail=process_logs, is_fail=is_fail)
                send_log(applied_form=applied_form, log=to_log)

        raise_if_cant_deduct_credit_document(applied_form)

        process_logs.append(["decrypt", str(datetime.now().isoformat())])

        serializer_encrypted = EncryptedSerializer(data=request.data)
        if not serializer_encrypted.is_valid():
            process_logs.append(["error_serializer_encrypted_header", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(serializer_encrypted.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        decrypted_json = decrypted_data = None
        try:
            decrypted_json = decrypt_header(serializer_encrypted.data["data"])
            decrypted_data = json.loads(decrypted_json)
            serializer = CardSubmitSerializer(data=decrypted_data)
        except Exception as e:
            logger.error("Error decrypting header:", e)
            process_logs.append(["error_decrypt_header", str(datetime.now().isoformat()), decrypted_json])
            __send_info_log(True)
            return Response(serializer_encrypted.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
        if not serializer.is_valid():
            process_logs.append(["error_serializer_invalid", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        nonce = serializer.data.get("nonce")
        ensure_facecompare = serializer.data.get("ensure_facecompare")
        logs = serializer.data.get("logs")
        frontend_process_logs = serializer.data.get("process_logs") or []
        process_logs = frontend_process_logs + process_logs

        # Submit request_camera_logs
        if logs and applied_form:
            for log in logs:
                action = f"action_log_{self.log_name}"
                to_log = {
                    "media": "document",
                    "code": action,
                    "error": log,
                    "log_type": "info",
                }
                send_log(applied_form=applied_form, log=to_log)

        # Process files
        files = []
        for key, value in request.FILES.items():
            files.extend(request.FILES.getlist(key))

        # Error: Wrong number of files
        if len(files) != 1:
            response = {"files": "invalid files"}
            process_logs.append(["error_invalid_files", str(datetime.now().isoformat()), response])
            __send_info_log(True)
            return Response(response, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        # Get ekyc
        process_logs.append(["get_ekyc", str(datetime.now().isoformat())])
        _ekyc, _ = Ekyc.objects.get_or_create(ref=ekyc_ref)
        ekyc: Ekyc = _ekyc

        form = applied_form.form if applied_form else None
        item_name = request.GET.get("item", None)
        schema = get_form_or_document_item(form, item_name)

        api_version = get_form_settings(form, f"ekyc.{self.model_name}.api_version", None)

        process_logs.append(["check_nonce", str(datetime.now().isoformat())])
        already_submitted_nonce = ekyc.get_children_set(self.model_name).filter(nonce=nonce).first()
        if already_submitted_nonce is not None:
            try:
                result = json.loads(already_submitted_nonce.result_response)
                code = result.get("code", status.HTTP_400_BAD_REQUEST)
                process_logs.append(["error_already_submitted_nonce", str(datetime.now().isoformat())])
                __send_info_log(True)
                return Response(result, status=code)
            except Exception:
                process_logs.append(["error_invalid_nonce", str(datetime.now().isoformat())])
                __send_info_log(True)
                return Response(
                    self.build_response(ekyc=ekyc, error_type="invalid_nonce"),
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if not ekyc.get_has_attempt(self.model_name):
            process_logs.append(["error_max_attempt", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(
                self.build_response(ekyc=ekyc, error_type="max_attempt"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        process_logs.append(["get_additional_header", str(datetime.now().isoformat())])
        # Get additional_header from formsettings
        additional_header_settings: dict[str, str] = get_form_settings(form, "ekyc.front_card.additional_header", {})

        additional_header = get_additional_header(request, serializer, form)
        additional_header = {
            **additional_header,
            **additional_header_settings,
        }

        process_logs.append(["get_additional_body", str(datetime.now().isoformat())])
        additional_body = get_additional_body(
            serializer, form, applied_form, self.model_name, self.document_type, schema
        )

        if self.model_name == "back_card" and ekyc.frontcard_upload:
            additional_body["front_document_request_id"] = ekyc.frontcard_upload

        ####################################################################
        #                           Real Request
        ####################################################################
        process_logs.append(["upload_to_api_gateway", str(datetime.now().isoformat())])
        response, upload_status = self.call_uploader(
            file=files[0],
            transaction_id=str(ekyc_ref),
            api_version=api_version,
            additional_header=additional_header,
            additional_body=additional_body,
            process_logs=process_logs,
            logs=logs,
        )

        # Inject response
        # Note: inject accepted_document_types for check_by_schema_conditions
        response["accepted_document_types"] = serializer.data.get("accepted_document_types")

        # Extract OCR
        resolved_document_type = response.get("document_type")
        process_logs.append(["extract_ocr", str(datetime.now().isoformat())])
        ocr = get(response, ["ocr"], {})
        if isinstance(ocr, dict):
            added_ocr = extract_document(resolved_document_type, ocr)
        else:
            added_ocr = {}

        # Check form conditions
        process_logs.append(["check_form_conditions", str(datetime.now().isoformat())])
        pass_all_checks, _, _ = check_by_schema_conditions(
            schema=schema, response=response, with_ocr=True, ocr_result=added_ocr, applied_form=applied_form
        )

        is_success = status.is_success(upload_status) and pass_all_checks

        # OCR Autofills
        process_logs.append(["ocr_autofill", str(datetime.now().isoformat())])
        if is_success:
            autofill_by_schema(
                schema=schema,
                ocr_result=added_ocr,
                document_type=resolved_document_type,
                applied_form=applied_form,
                response=response,
            )
        else:
            set_(response, ["autofill", "skipped"], True)

        # Save Document
        process_logs.append(["save_document", str(datetime.now().isoformat())])
        set_document(
            ekyc,
            model_name=self.model_name,
            response=response,
            nonce=nonce,
            passed=is_success,
            process_logs=process_logs,
        )

        response_to_return = pick(response, ["code", "ocr", "document_type", "data.result", "data.image_url"])

        ensure_facecompare_success = do_ensure_facecompare(
            ekyc, ensure_facecompare=ensure_facecompare, process_logs=process_logs
        )

        process_logs.append(["build_result_url", str(datetime.now().isoformat())])
        if is_success and ensure_facecompare_success:
            response_to_return["result_url"] = ekyc.get_answer_url(self.field_name)

        process_logs.append(["build_compare_url", str(datetime.now().isoformat())])
        if ekyc.has_comparable_pair():
            response_to_return["compare_url"] = ekyc.compare_url

        if status.is_client_error(upload_status):
            process_logs.append(["error_api_gateway_client", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(
                self.build_response(response_to_return, ekyc, "api_gateway_client_error"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not ensure_facecompare_success:
            process_logs.append(["error_face_compare_failed", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(
                self.build_response(response_to_return, ekyc, "face_compare_failed"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Recheck again if reached attempt
        if not is_success and not ekyc.get_has_attempt(self.model_name):
            process_logs.append(["error_max_attempt", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(
                self.build_response(response_to_return, ekyc, "max_attempt"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        process_logs.append(["completed", str(datetime.now().isoformat())])
        __send_info_log(False)
        return Response(
            self.build_response(response_to_return, ekyc=ekyc, call_webhook_max_attempt=False),
            status=upload_status,
        )

    @action(detail=False, methods=["post"])
    def quality(self, request, ekyc_ref, **kwargs):
        applied_form = get_applied_form(ekyc_ref)

        raise_if_cant_deduct_credit_document(applied_form)

        serializer_encrypted = EncryptedSerializer(data=request.data)
        if not serializer_encrypted.is_valid():
            return Response(serializer_encrypted.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        decrypted_json = decrypted_data = None
        try:
            decrypted_json = decrypt_header(serializer_encrypted.data["data"])
            decrypted_data = json.loads(decrypted_json)
            serializer = CheckQualitySerializer(data=decrypted_data)
        except Exception:
            to_log = {
                "media": "document",
                "code": f"error_backend_quality_{self.log_name}",
                "error": decrypted_json,
                "log_type": "error",
            }
            send_log(applied_form=applied_form, log=to_log)
            return Response(serializer_encrypted.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        files = []
        for key, value in request.FILES.items():
            files.extend(request.FILES.getlist(key))

        # Error: Wrong number of files
        if len(files) != 1:
            response = {"files": "invalid files"}
            return Response(response, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        # Get ekyc
        _ekyc, _ = Ekyc.objects.get_or_create(ref=ekyc_ref)
        ekyc: Ekyc = _ekyc

        form = applied_form.form if applied_form else None

        api_version = get_form_settings(form, f"ekyc.{self.model_name}.quality_api_version", None)
        features = get_form_settings(form, f"ekyc.{self.model_name}.features", {})

        if not ekyc.get_has_attempt(self.model_name):
            return Response(
                self.build_response(ekyc=ekyc, error_type="max_attempt"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        additional_header = get_additional_header(request, serializer, form)

        # Upload
        response, quality_api_status = self.call_check_quality(
            file=files[0],
            transaction_id=str(ekyc_ref),
            api_version=api_version,
            additional_header=additional_header,
            additional_body={"features": json.dumps(features)},
        )

        # Result (for preview_image)
        if status.is_success(quality_api_status):
            request_id = response.get("request_id")
            preview_url = reverse("ekyc:result-image", kwargs={"ekyc_ref": ekyc_ref}, request=request)
            preview_url = encrypt_url(
                preview_url,
                obj={
                    "ref": ekyc_ref,
                    "document_type": self.document_type,
                    "request_id": request_id,
                },
                timeout=EKYC_RESULT_IMAGE_TIMEOUT,
            )
            response["preview_url"] = preview_url

        to_log = {
            "media": "document",
            "code": f"request_quality_api_{self.log_name}",
            "error": response,
            "log_type": "info" if status.is_success(quality_api_status) else "error",
        }
        send_log(applied_form=applied_form, log=to_log)

        return Response(
            self.build_response(response, ekyc=ekyc, call_webhook_max_attempt=False),
            status=quality_api_status,
        )
