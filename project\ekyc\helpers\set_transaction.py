from pydash import get
from datetime import datetime

from ..models import Ekyc
from ..models.liveness import *
from ..models.front_card import *
from ..models.back_card import *
from .credit import deduct_credit_liveness, deduct_credit_document
from .log import add_applied_form_log


FEATURE_CREDIT_LIVENESS = "liveness_verification"
FEATURE_CREDIT_DOCUMENT = "id_document_verification_ocr"


def set_liveness(ekyc: Ekyc, liveness: Liveness, response, passed=True, process_logs=None):
    if process_logs is None:
        process_logs = []

    liveness.upload_response = get(response, ["data"])
    liveness.result_response = response
    if liveness.status in [STATUS_STARTED]:
        liveness.status = STATUS_SUCCESS if passed else STATUS_FAILED_BACKEND
    liveness.save()

    process_logs.append(["call_webhook_liveness", str(datetime.now().isoformat())])
    liveness.call_webhook()

    if not passed:
        return

    process_logs.append(["deduct_credit", str(datetime.now().isoformat())])
    deduct_credit_liveness(ekyc.applied_form)


def set_document(ekyc: Ekyc, model_name, response, nonce=None, passed=True, process_logs=None):
    if process_logs is None:
        process_logs = []

    obj = None

    if model_name == "front_card":
        new_front_card: FrontCard = ekyc.frontcard_set.create(
            ekyc=ekyc,
            upload_response=get(response, ["data", "request_id"]),
            result_response=response,
            nonce=nonce,
            is_success=passed,
        )
        process_logs.append(["call_webhook_front_card", str(datetime.now().isoformat())])
        new_front_card.call_webhook(is_success=passed)
        obj = new_front_card

    elif model_name == "back_card":
        new_back_card: BackCard = ekyc.backcard_set.create(
            ekyc=ekyc,
            upload_response=get(response, ["data", "request_id"]),
            result_response=response,
            nonce=nonce,
            is_success=passed,
        )
        process_logs.append(["call_webhook_back_card", str(datetime.now().isoformat())])
        new_back_card.call_webhook(is_success=passed)
        obj = new_back_card

    process_logs.append(["deduct_credit", str(datetime.now().isoformat())])
    applied_form = ekyc.applied_form
    if applied_form:
        add_applied_form_log(applied_form=applied_form, model_name=model_name, ekyc_item_obj=obj)
    deduct_credit_document(ekyc.applied_form)


def do_ensure_facecompare(ekyc: Ekyc, ensure_facecompare=False, process_logs=None):
    if process_logs is None:
        process_logs = []

    if not ekyc:
        return True

    if not ensure_facecompare:
        return True

    # Ensure facecompare call
    process_logs.append(["request_face_compare", str(datetime.now().isoformat())])

    is_compare_success, facecompare = ekyc.requestFaceCompare()

    # No pair
    if is_compare_success == None and facecompare == None:
        return True

    is_report_compare_success = False
    if is_compare_success:
        process_logs.append(["get_report_face_compare", str(datetime.now().isoformat())])
        compare_response, facecompare = ekyc.getReportFaceCompare()
        is_report_compare_success = compare_response["status"]

    # hook ekyc
    return is_report_compare_success
