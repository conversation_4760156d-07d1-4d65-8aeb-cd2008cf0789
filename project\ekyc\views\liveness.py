import json
from pydash import get
from rest_framework import status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ars<PERSON>
from rest_framework.reverse import reverse
from rest_framework.decorators import action
from typing import Tuple
from datetime import datetime
from django.shortcuts import get_object_or_404

from ..helpers.file_hash import get_file_hash
from ..helpers.log import send_log, add_applied_form_log
from ..helpers.utils import get_additional_header
from ..helpers.faceactions import FaceActions
from ..helpers.external_api import upload_liveness
from ..helpers.set_transaction import do_ensure_facecompare, set_liveness
from ..helpers.url_encryption import encrypt_header
from ..helpers.sjcl_encryption import decrypt_header
from ..helpers.get_form import get_applied_form, get_form_liveness_item, get_form_answer, get_form_settings
from ..helpers.credit import raise_if_cant_deduct_credit_liveness
from ..models import Ekyc, Liveness, LivenessLog
from ..models.liveness import (
    STATUS_FAILED_BACKEND,
    STATUS_PENDING,
    STATUS_CANCELLED,
    STATUS_STARTED,
    STATUS_FAILED_FRONTEND,
)
from ..serializers import (
    EncryptedSerializer,
    LivenessActionSerializer,
    LivenessSubmitSerializer,
    FaceActionSerializer,
)
from .base_ekyc import BaseEkycViewset
from known_face.known_face import KnownFaceApp


def get_additional_body(form, applied_form, process_logs: list = None):
    if process_logs is None:
        process_logs = []

    # Get features
    process_logs.append(["get_body_features", str(datetime.now().isoformat())])
    features = get_form_settings(form, "ekyc.liveness.features", {})

    # Get additional_body from formsettings
    process_logs.append(["get_body_settings", str(datetime.now().isoformat())])
    additional_body_settings: dict[str, str] = get_form_settings(form, "ekyc.liveness.additional_body", {})

    # Get all answers_to_upload
    process_logs.append(["get_body_answers", str(datetime.now().isoformat())])
    answers_to_upload_map: dict[str, str] = get_form_settings(form, "ekyc.liveness.answers_to_upload", {})
    answers_to_upload = {}
    if answers_to_upload_map and applied_form:
        for dest, src in answers_to_upload_map.items():
            answer = get_form_answer(applied_form, src)
            if not isinstance(answer, str):
                answer = json.dumps(answer)
            answers_to_upload[dest] = answer

    return {
        "features": json.dumps(features),
        **additional_body_settings,
        **answers_to_upload,
    }


class LivenessViewSet(BaseEkycViewset):
    basename = "liveness"
    parent_lookup_kwargs = {"ekyc": "ekyc"}
    parser_classes = (
        MultiPartParser,
        JSONParser,
    )

    model_name = "liveness"
    log_name = "liveness"

    @action(detail=False, methods=["post"])
    def faceaction(self, request, ekyc_ref, *args, **kwargs):
        serializer = FaceActionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        ekyc, created = Ekyc.objects.get_or_create(ref=ekyc_ref)
        if not ekyc.liveness_has_attempt:
            return Response(
                self.build_response(ekyc=ekyc, error_type="max_attempt"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        faceaction_pool_list = None
        action_sequence = None
        try:
            applied_form = get_applied_form(ekyc.ref)
            if applied_form:
                form = applied_form.form
                schema = get_form_liveness_item(form)
                enable_idle_only = get(schema, ["liveness", "enableIdleOnly"])
                if enable_idle_only:
                    faceaction_pool_list = ["idle"]
                else:
                    faceaction_pool_list = get(schema, ["configs", "faceaction_pool_list"])

                action_sequence = get(schema, ["liveness", "action_sequence"], [])
        except Exception as err:
            print(err)

        faceactions = FaceActions(faceaction_pool_list)
        actions = faceactions.actions
        if not request.session.session_key:
            request.session.create()
        liveness: Liveness = ekyc.liveness_set.create(
            face_actions=json.dumps(actions),
            face_actions_expire_at=faceactions.expireAt,
            action_sequence=json.dumps(action_sequence),
            log=json.dumps(serializer.data.get("log")),
        )
        add_applied_form_log(applied_form=applied_form, model_name=self.model_name, ekyc_item_obj=liveness)
        request.session["liveness_id"] = liveness.id
        result_response = {
            "liveness_actions": actions,
            "action_sequence": action_sequence,
            "expire_at": str(faceactions.expireAt),
            "upload_url": reverse("ekyc:liveness-submit", kwargs={"ekyc_ref": ekyc_ref}, request=request)
            + f"?ref={liveness.id}",
            "log_url": reverse("ekyc:liveness-log", kwargs={"ekyc_ref": ekyc_ref}, request=request)
            + f"?ref={liveness.id}",
            "start_url": reverse("ekyc:liveness-start", kwargs={"ekyc_ref": ekyc_ref}, request=request)
            + f"?ref={liveness.id}",
            "cancel_url": reverse("ekyc:liveness-cancel", kwargs={"ekyc_ref": ekyc_ref}, request=request)
            + f"?ref={liveness.id}",
            "fail_url": reverse("ekyc:liveness-fail", kwargs={"ekyc_ref": ekyc_ref}, request=request)
            + f"?ref={liveness.id}",
        }
        result_response_encrypted = encrypt_header(json.dumps(result_response), request.session.session_key)
        liveness.call_webhook(action_status=STATUS_PENDING)
        return Response(result_response_encrypted, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"])
    def start(self, request, *args, **kwargs):
        ref = request.GET.get("ref", None)
        liveness_id = request.session.get("liveness_id", None)
        if str(liveness_id) != str(ref):
            return Response(
                self.build_response(error_type="session_invalid"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = LivenessActionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
        liveness = get_object_or_404(Liveness, id=ref)
        ekyc = liveness.ekyc

        if "logs" in serializer.data:
            self.save_logs(ref, serializer.data.get("logs"))

        if not ekyc.liveness_has_attempt:
            return Response(
                self.build_response(ekyc=ekyc, error_type="max_attempt"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        if liveness.status == STATUS_PENDING:
            liveness.status = STATUS_STARTED
            liveness.save()
            add_applied_form_log(applied_form=ekyc.applied_form, model_name=self.model_name, ekyc_item_obj=liveness)
            liveness.call_webhook(action_status=STATUS_STARTED)
            return Response(
                self.build_response(ekyc=ekyc, call_webhook_max_attempt=False),
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                self.build_response(ekyc=ekyc, error_type="Cannot start this liveness test"),
                status=status.HTTP_400_BAD_REQUEST,
            )

    def handle_log_apis(self, request, attempt_status, *args, **kwargs) -> Tuple[bool, Response, Liveness]:
        ref = request.GET.get("ref", None)
        liveness = get_object_or_404(Liveness, id=ref)
        liveness_id = request.session.get("liveness_id", None)
        if str(liveness_id) != str(ref):
            return (
                False,
                Response(
                    self.build_response(error_type="session_invalid"),
                    status=status.HTTP_400_BAD_REQUEST,
                ),
                liveness,
            )

        upload_response = None
        serializer_encrypted = EncryptedSerializer(data=request.data)
        if serializer_encrypted.is_valid():
            decrypted_json = decrypted_data = None
            try:
                decrypted_json = decrypt_header(serializer_encrypted.data["data"])
                decrypted_data = json.loads(decrypted_json)
                serializer = LivenessActionSerializer(data=decrypted_data)
            except Exception as e:
                print(e)
                response = {"files": "invalid data"}
                return (
                    False,
                    Response(
                        self.build_response(response, error_type="fail_api_data_corrupted"),
                        status=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    ),
                    liveness,
                )

            if not serializer.is_valid():
                return False, Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY), liveness

            files = []
            for key, value in request.FILES.items():
                files.extend(request.FILES.getlist(key))

            # Upload failed images to api-gateway
            try:
                # Get ekyc
                ekyc = liveness.ekyc
                applied_form = get_applied_form(ekyc.ref)
                form = applied_form.form if applied_form else None
                api_version = get_form_settings(form, "ekyc.liveness.api_version", None)

                # Get additional_header from formsettings
                additional_header_settings: dict[str, str] = get_form_settings(
                    form, "ekyc.liveness.additional_header", {}
                )

                additional_header = get_additional_header(request, serializer, form)
                additional_header = {
                    "attempt_status": attempt_status,
                    **additional_header,
                    **additional_header_settings,
                }

                # Get additional_body from formsettings
                additional_body = get_additional_body(form, applied_form)

                response, upload_status = upload_liveness(
                    files=files,
                    transaction_id=str(ekyc.ref),
                    logs=serializer.data.get("logs"),
                    api_version=api_version,
                    additional_header=additional_header,
                    additional_body=additional_body,
                )
                if upload_status == 200:
                    upload_response = get(response, ["data"])
                else:
                    upload_response = get(response, ["request_id"])
                liveness.upload_response = upload_response
                liveness.result_response = response
                # Will be saved later
            except Exception as e:
                print("Cant upload failed image to api-gateway")
                print(e)

            if "logs" in serializer.data:
                self.save_logs(ref, serializer.data.get("logs"))
        return True, None, liveness

    @action(detail=False, methods=["post"])
    def cancel(self, request, *args, **kwargs):
        accepted, response, liveness = self.handle_log_apis(request, attempt_status="cancel", *args, **kwargs)
        if not accepted:
            return response

        ekyc = liveness.ekyc
        if liveness.status == STATUS_STARTED:
            liveness.status = STATUS_CANCELLED
            liveness.save()
            add_applied_form_log(applied_form=ekyc.applied_form, model_name=self.model_name, ekyc_item_obj=liveness)
            liveness.call_webhook(action_status=STATUS_CANCELLED)
            response = self.build_response(ekyc=ekyc)

            # If can compare, send link
            if ekyc.has_comparable_pair():
                response["compare_url"] = ekyc.compare_url

            return Response(response, status=status.HTTP_200_OK)
        else:
            return Response(
                self.build_response(error_type="Cannot cancel this liveness test", ekyc=ekyc),
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["post"])
    def fail(self, request, *args, **kwargs):
        accepted, response, liveness = self.handle_log_apis(request, attempt_status="fail", *args, **kwargs)
        if not accepted:
            return response

        ekyc = liveness.ekyc
        if liveness.status == STATUS_STARTED:
            liveness.status = STATUS_FAILED_FRONTEND
            liveness.save()
            add_applied_form_log(applied_form=ekyc.applied_form, model_name=self.model_name, ekyc_item_obj=liveness)
            liveness.call_webhook(action_status=STATUS_FAILED_FRONTEND)
            response = self.build_response(ekyc=ekyc)

            # If can compare, send link
            if ekyc.has_comparable_pair():
                response["compare_url"] = ekyc.compare_url

            return Response(response, status=status.HTTP_200_OK)
        else:
            return Response(
                self.build_response(error_type="Liveness test was not started yet", ekyc=ekyc),
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["post"])
    def submit(self, request, ekyc_ref, *args, **kwargs):
        process_logs = [["backend_start", str(datetime.now().isoformat())]]
        applied_form = get_applied_form(ekyc_ref)

        raise_if_cant_deduct_credit_liveness(applied_form)

        def __send_info_log(is_fail=False):
            if applied_form:
                action = f"info_backend_submit_{self.log_name}"
                to_log = {
                    "media": "liveness",
                    "code": action,
                    "error": process_logs,
                    "log_type": "info",
                }
                applied_form.log(action=action, detail=process_logs, is_fail=is_fail)
                send_log(applied_form=applied_form, log=to_log)

        ref = request.GET.get("ref", None)

        liveness_id = request.session.get("liveness_id", None)
        if str(liveness_id) != str(ref):
            return Response(
                self.build_response(error_type="session_invalid"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        process_logs.append(["decrypt", str(datetime.now().isoformat())])

        serializer_encrypted = EncryptedSerializer(data=request.data)
        if not serializer_encrypted.is_valid():
            process_logs.append(["error_serializer_encrypted_header", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(serializer_encrypted.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        decrypted_json = decrypted_data = None
        try:
            decrypted_json = decrypt_header(serializer_encrypted.data["data"])
            decrypted_data = json.loads(decrypted_json)
            serializer = LivenessSubmitSerializer(data=decrypted_data)
        except Exception:
            process_logs.append(["error_decrypt_header", str(datetime.now().isoformat()), decrypted_json])
            __send_info_log(True)
            return Response(serializer_encrypted.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
        if not serializer.is_valid():
            process_logs.append(["error_serializer_invalid", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        filehashes = serializer.data.get("filehashes")
        # nonce = serializer.data.get("nonce")
        # device_type = serializer.data.get("device_type")
        ensure_facecompare = serializer.data.get("ensure_facecompare")
        frontend_process_logs = serializer.data.get("process_logs") or []
        process_logs = frontend_process_logs + process_logs

        files = []
        for key, value in request.FILES.items():
            files.extend(request.FILES.getlist(key))

        # Error: No file
        if len(files) == 0:
            response = {"files": "invalid files"}
            process_logs.append(["error_no_file", str(datetime.now().isoformat()), response])
            __send_info_log(True)
            return Response(
                self.build_response(response, error_type="file_corrupted"),
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        # Get ekyc
        process_logs.append(["get_ekyc", str(datetime.now().isoformat())])
        liveness = get_object_or_404(Liveness, id=ref)
        ekyc: Ekyc = liveness.ekyc

        form = applied_form.form if applied_form else None
        api_version = get_form_settings(form, "ekyc.liveness.api_version", None)

        face_actions = json.loads(liveness.face_actions)

        # Combine init log with submit log
        submit_log = serializer.data.get("submit_log")
        if submit_log:
            updated_log = json.loads(liveness.log or "{}")
            updated_log["submit"] = submit_log
            liveness.log = json.dumps(updated_log)

        # Error: Not Started
        if liveness.status != STATUS_STARTED:
            response = {
                "media": "liveness",
                "code": "error_backend_submit_liveness_cant_submit",
                "error": f"Cannot submit this liveness test: {liveness.status}",
                "log_type": "error",
            }
            process_logs.append(["error_not_started", str(datetime.now().isoformat()), response])
            __send_info_log(True)
            return Response(
                self.build_response({"error": response["error"]}, ekyc=ekyc, error_type="cant_submit"),
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        try:
            process_logs.append(["check_files", str(datetime.now().isoformat())])
            check_hashes_dict = {}
            check_hashes_list = []
            files_sorted = []
            for i, file in enumerate(files):
                key = get_file_hash(file)
                check_hashes_dict[key] = file.name
                check_hashes_list.append(key)

            # Error: File hash
            if len(check_hashes_list) != len(filehashes):
                raise AttributeError("File hash length error")

            for i, filehash in enumerate(filehashes):
                that_file = next(file for file in files if file.name == check_hashes_dict[filehash])
                if not that_file:
                    raise AttributeError("File hash error")
                files_sorted.append(that_file)

            # Error: File name / count
            filenames = [file.name.split(".")[0] for file in files]
            required_filenames = []
            counter_map = {}
            for i, fa in enumerate(face_actions):
                # for j in range(0, 2):
                required_filenames.append(f"{fa}_{counter_map.get(fa, 0)}")
                counter_map[fa] = counter_map.get(fa, 0) + 1

            if len(filenames) < len(required_filenames):
                raise AttributeError(f"File name length error: {filenames} < {required_filenames}")

            for i, required_filename in enumerate(required_filenames):
                if required_filename not in filenames:
                    raise AttributeError("File name error")
        except Exception as e:
            liveness.status = STATUS_FAILED_BACKEND
            liveness.save()
            add_applied_form_log(applied_form=ekyc.applied_form, model_name=self.model_name, ekyc_item_obj=liveness)
            liveness.call_webhook(action_status=STATUS_FAILED_BACKEND)
            response = {
                "media": "liveness",
                "code": "error_backend_submit_liveness_file_corrupted",
                "error": "Unprocessable Entity",
                "log_type": "error",
            }
            process_logs.append(
                ["error_backend_submit_liveness_file_corrupted", str(datetime.now().isoformat()), response]
            )
            __send_info_log(True)
            print(response, e)
            return Response(
                self.build_response({"files": response["error"]}, ekyc, "file_corrupted"),
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        # Save logs
        process_logs.append(["save_logs", str(datetime.now().isoformat())])
        if "logs" in serializer.data:
            self.save_logs(ref, serializer.data.get("logs"))

        # Get additional_body from formsettings
        process_logs.append(["get_additional_body", str(datetime.now().isoformat())])
        additional_body = get_additional_body(form, applied_form, process_logs=process_logs)

        # Get perform_face_compare
        comparable_document = None
        if get_form_settings(form, "ekyc.liveness.perform_face_compare", False):
            _, comparable_document = ekyc.get_comparable_pair()
            if comparable_document:
                additional_body["perform_face_compare"] = True
                additional_body["id_doc_request_id"] = comparable_document.upload_response

        # Get additional_header from formsettings
        additional_header_settings: dict[str, str] = get_form_settings(form, "ekyc.liveness.additional_header", {})

        additional_header = get_additional_header(request, serializer, form)
        additional_header = {
            **additional_header,
            **additional_header_settings,
        }

        ####################################################################
        #                           Real Request
        ####################################################################
        response, upload_status = upload_liveness(
            files=files,
            transaction_id=str(ekyc.ref),
            logs=serializer.data.get("logs"),
            api_version=api_version,
            additional_header=additional_header,
            additional_body=additional_body,
            process_logs=process_logs,
        )

        is_success = status.is_success(upload_status)

        if is_success:
            # Check form ignore
            process_logs.append(["check_form_conditions", str(datetime.now().isoformat())])
            form = applied_form.form if applied_form else None
            schema = get_form_liveness_item(form)
            ignore = get(schema, ["configs", "ignore"], {})
            for key, value in get(response, "result", {}).items():
                passed = value.get("status", False) or ignore.get(key, False)
                if not passed:
                    is_success = False
                    break

        process_logs.append(["save_liveness", str(datetime.now().isoformat())])
        set_liveness(
            ekyc,
            liveness,
            response=response,
            passed=is_success,
            process_logs=process_logs,
        )
        add_applied_form_log(applied_form=applied_form, model_name=self.model_name, ekyc_item_obj=liveness)

        process_logs.append(["register_known_face", str(datetime.now().isoformat())])
        know_face = KnownFaceApp(form=form, applied_form=applied_form, ekyc=ekyc)
        know_face.register_known_face(liveness=liveness, process_logs=process_logs)

        ##############################################
        # Face Compare
        ##############################################
        if is_success:
            did_perform_face_compare = False
            did_ensure_facecompare = False
            ensure_facecompare_success = False

            # Save facecompare from response
            process_logs.append(["perform_face_compare", str(datetime.now().isoformat())])
            did_perform_face_compare, perform_facecompare_success = ekyc.save_perform_face_compare(
                liveness=liveness, document=comparable_document, response=response
            )
            if did_perform_face_compare:
                # No need to ensure facecompare since we already have the result
                did_ensure_facecompare = perform_facecompare_success
                # Note: We don't treat perform_face_compare failure as fatal
                # It just means we couldn't save the face compare from the response
                # The user can still use the compare_url for manual comparison

            # Ensure facecompare
            if ensure_facecompare and not did_ensure_facecompare:
                did_ensure_facecompare = True
                ensure_facecompare_success = do_ensure_facecompare(
                    ekyc, ensure_facecompare=ensure_facecompare, process_logs=process_logs
                )
                if not ensure_facecompare_success:
                    process_logs.append(["error_face_compare_failed", str(datetime.now().isoformat())])
                    __send_info_log(True)
                    return Response(
                        self.build_response(response, ekyc, "face_compare_failed"),
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Success = Will give frontend url to call facecompare if no facecompare result
            if not did_perform_face_compare and not did_ensure_facecompare:
                process_logs.append(["build_compare_url", str(datetime.now().isoformat())])
                if ekyc.has_comparable_pair():
                    response["compare_url"] = ekyc.compare_url

            # Success = Build result url
            process_logs.append(["build_result_url", str(datetime.now().isoformat())])
            if is_success:
                response["result_url"] = ekyc.get_answer_url("Liveness")

        # Max attempt
        if not is_success and not ekyc.liveness_has_attempt:
            process_logs.append(["error_max_attempt", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(
                self.build_response(response, ekyc, "max_attempt"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # 4xx
        if status.is_client_error(upload_status):
            process_logs.append(["error_api_gateway_client", str(datetime.now().isoformat())])
            __send_info_log(True)
            return Response(
                self.build_response(response, ekyc, "api_gateway_client_error"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        process_logs.append(["completed", str(datetime.now().isoformat())])
        __send_info_log(False)
        return Response(
            self.build_response(response, ekyc=ekyc, call_webhook_max_attempt=False),
            status=upload_status,
        )

    @action(detail=False, methods=["post"])
    def log(self, request, *args, **kwargs):
        ref = request.GET.get("ref", None)
        liveness_id = request.session.get("liveness_id", None)
        if str(liveness_id) != str(ref):
            return Response(
                self.build_response(error_type="session_invalid"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        logs = request.data
        if not logs:
            return Response(
                self.build_response(error_type="No log array"),
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        self.save_logs(ref, logs)

        return Response(f"Saved {len(logs)} logs")

    def save_logs(self, ref, logs: list[dict]):
        if not logs:
            return

        for log in logs:
            try:
                saved_log = LivenessLog(
                    liveness=get_object_or_404(Liveness, id=ref),
                    action=log.get("action", ""),
                    color=log.get("color", ""),
                    frame_number=log.get("frame_number"),
                    result=json.dumps(log.get("result")),
                    recorded_at=log.get("recorded_at"),
                )
                saved_log.save()
            except:
                pass
