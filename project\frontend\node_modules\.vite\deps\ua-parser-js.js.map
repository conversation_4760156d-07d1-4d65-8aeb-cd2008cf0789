{"version": 3, "sources": ["../../ua-parser-js/src/ua-parser.js"], "sourcesContent": ["/////////////////////////////////////////////////////////////////////////////////\n/* UAParser.js v1.0.41\n   Copyright © 2012-2025 F<PERSON><PERSON> <<EMAIL>>\n   MIT License *//*\n   Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data.\n   Supports browser & node.js environment. \n   Demo   : https://faisalman.github.io/ua-parser-js\n   Source : https://github.com/faisalman/ua-parser-js */\n/////////////////////////////////////////////////////////////////////////////////\n\n(function (window, undefined) {\n\n    'use strict';\n\n    //////////////\n    // Constants\n    /////////////\n\n\n    var LIBVERSION  = '1.0.41',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n        MAJOR       = 'major',\n        MODEL       = 'model',\n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        EMBEDDED    = 'embedded',\n        UA_MAX_LENGTH = 500;\n\n    var AMAZON  = 'Amazon',\n        APPLE   = 'Apple',\n        ASUS    = 'ASUS',\n        BLACKBERRY = 'BlackBerry',\n        BROWSER = 'Browser',\n        CHROME  = 'Chrome',\n        EDGE    = 'Edge',\n        FIREFOX = 'Firefox',\n        GOOGLE  = 'Google',\n        HONOR   = 'Honor',\n        HUAWEI  = 'Huawei',\n        LENOVO  = 'Lenovo',\n        LG      = 'LG',\n        MICROSOFT = 'Microsoft',\n        MOTOROLA  = 'Motorola',\n        NVIDIA  = 'Nvidia',\n        ONEPLUS = 'OnePlus',\n        OPERA   = 'Opera',\n        OPPO    = 'OPPO',\n        SAMSUNG = 'Samsung',\n        SHARP   = 'Sharp',\n        SONY    = 'Sony',\n        XIAOMI  = 'Xiaomi',\n        ZEBRA   = 'Zebra',\n        FACEBOOK    = 'Facebook',\n        CHROMIUM_OS = 'Chromium OS',\n        MAC_OS  = 'Mac OS',\n        SUFFIX_BROWSER = ' Browser';\n\n    ///////////\n    // Helper\n    //////////\n\n    var extend = function (regexes, extensions) {\n            var mergedRegexes = {};\n            for (var i in regexes) {\n                if (extensions[i] && extensions[i].length % 2 === 0) {\n                    mergedRegexes[i] = extensions[i].concat(regexes[i]);\n                } else {\n                    mergedRegexes[i] = regexes[i];\n                }\n            }\n            return mergedRegexes;\n        },\n        enumerize = function (arr) {\n            var enums = {};\n            for (var i=0; i<arr.length; i++) {\n                enums[arr[i].toUpperCase()] = arr[i];\n            }\n            return enums;\n        },\n        has = function (str1, str2) {\n            return typeof str1 === STR_TYPE ? lowerize(str2).indexOf(lowerize(str1)) !== -1 : false;\n        },\n        lowerize = function (str) {\n            return str.toLowerCase();\n        },\n        majorize = function (version) {\n            return typeof(version) === STR_TYPE ? version.replace(/[^\\d\\.]/g, EMPTY).split('.')[0] : undefined;\n        },\n        trim = function (str, len) {\n            if (typeof(str) === STR_TYPE) {\n                str = str.replace(/^\\s\\s*/, EMPTY);\n                return typeof(len) === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);\n            }\n    };\n\n    ///////////////\n    // Map helper\n    //////////////\n\n    var rgxMapper = function (ua, arrays) {\n\n            var i = 0, j, k, p, q, matches, match;\n\n            // loop through all regexes maps\n            while (i < arrays.length && !matches) {\n\n                var regex = arrays[i],       // even sequence (0,2,4,..)\n                    props = arrays[i + 1];   // odd sequence (1,3,5,..)\n                j = k = 0;\n\n                // try matching uastring with regexes\n                while (j < regex.length && !matches) {\n\n                    if (!regex[j]) { break; }\n                    matches = regex[j++].exec(ua);\n\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length === 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        this[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        this[q[0]] = q[1];\n                                    }\n                                } else if (q.length === 3) {\n                                    // check whether function or regex\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        // call function (usually string mapper)\n                                        this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                    } else {\n                                        // sanitize match using given regex\n                                        this[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                    }\n                                } else if (q.length === 4) {\n                                        this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                }\n                            } else {\n                                this[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n        },\n\n        strMapper = function (str, map) {\n\n            for (var i in map) {\n                // check if current value is array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return map.hasOwnProperty('*') ? map['*'] : str;\n    };\n\n    ///////////////\n    // String map\n    //////////////\n\n    // Safari < 3.0\n    var oldSafariMap = {\n            '1.0'   : '/8',\n            '1.2'   : '/1',\n            '1.3'   : '/3',\n            '2.0'   : '/412',\n            '2.0.2' : '/416',\n            '2.0.3' : '/417',\n            '2.0.4' : '/419',\n            '?'     : '/'\n        },\n        windowsVersionMap = {\n            'ME'        : '4.90',\n            'NT 3.11'   : 'NT3.51',\n            'NT 4.0'    : 'NT4.0',\n            '2000'      : 'NT 5.0',\n            'XP'        : ['NT 5.1', 'NT 5.2'],\n            'Vista'     : 'NT 6.0',\n            '7'         : 'NT 6.1',\n            '8'         : 'NT 6.2',\n            '8.1'       : 'NT 6.3',\n            '10'        : ['NT 6.4', 'NT 10.0'],\n            'RT'        : 'ARM'\n    };\n\n    //////////////\n    // Regex map\n    /////////////\n\n    var regexes = {\n\n        browser : [[\n\n            /\\b(?:crmo|crios)\\/([\\w\\.]+)/i                                      // Chrome for Android/iOS\n            ], [VERSION, [NAME, 'Chrome']], [\n            /edg(?:e|ios|a)?\\/([\\w\\.]+)/i                                       // Microsoft Edge\n            ], [VERSION, [NAME, 'Edge']], [\n\n            // Presto based\n            /(opera mini)\\/([-\\w\\.]+)/i,                                        // Opera Mini\n            /(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,                 // Opera Mobi/Tablet\n            /(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i                           // Opera\n            ], [NAME, VERSION], [\n            /opios[\\/ ]+([\\w\\.]+)/i                                             // Opera mini on iphone >= 8.0\n            ], [VERSION, [NAME, OPERA+' Mini']], [\n            /\\bop(?:rg)?x\\/([\\w\\.]+)/i                                          // Opera GX\n            ], [VERSION, [NAME, OPERA+' GX']], [\n            /\\bopr\\/([\\w\\.]+)/i                                                 // Opera Webkit\n            ], [VERSION, [NAME, OPERA]], [\n\n            // Mixed\n            /\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i            // Baidu\n            ], [VERSION, [NAME, 'Baidu']], [\n            /\\b(?:mxbrowser|mxios|myie2)\\/?([-\\w\\.]*)\\b/i                       // Maxthon\n            ], [VERSION, [NAME, 'Maxthon']], [\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\\/ ]?([\\w\\.]*)/i,      \n                                                                                // Lunascape/Maxthon/Netfront/Jasmine/Blazer/Sleipnir\n            // Trident based\n            /(avant|iemobile|slim(?:browser|boat|jet))[\\/ ]?([\\d\\.]*)/i,        // Avant/IEMobile/SlimBrowser/SlimBoat/Slimjet\n            /(?:ms|\\()(ie) ([\\w\\.]+)/i,                                         // Internet Explorer\n\n            // Blink/Webkit/KHTML based                                         // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon\n            /(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\\/([-\\w\\.]+)/i,\n                                                                                // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ//Vivaldi/DuckDuckGo/Klar/Helio/Dragon\n            /(heytap|ovi|115)browser\\/([\\d\\.]+)/i,                              // HeyTap/Ovi/115\n            /(weibo)__([\\d\\.]+)/i                                               // Weibo\n            ], [NAME, VERSION], [\n            /quark(?:pc)?\\/([-\\w\\.]+)/i                                         // Quark\n            ], [VERSION, [NAME, 'Quark']], [\n            /\\bddg\\/([\\w\\.]+)/i                                                 // DuckDuckGo\n            ], [VERSION, [NAME, 'DuckDuckGo']], [\n            /(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i                 // UCBrowser\n            ], [VERSION, [NAME, 'UC'+BROWSER]], [\n            /microm.+\\bqbcore\\/([\\w\\.]+)/i,                                     // WeChat Desktop for Windows Built-in Browser\n            /\\bqbcore\\/([\\w\\.]+).+microm/i,\n            /micromessenger\\/([\\w\\.]+)/i                                        // WeChat\n            ], [VERSION, [NAME, 'WeChat']], [\n            /konqueror\\/([\\w\\.]+)/i                                             // Konqueror\n            ], [VERSION, [NAME, 'Konqueror']], [\n            /trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i                       // IE11\n            ], [VERSION, [NAME, 'IE']], [\n            /ya(?:search)?browser\\/([\\w\\.]+)/i                                  // Yandex\n            ], [VERSION, [NAME, 'Yandex']], [\n            /slbrowser\\/([\\w\\.]+)/i                                             // Smart Lenovo Browser\n            ], [VERSION, [NAME, 'Smart Lenovo '+BROWSER]], [\n            /(avast|avg)\\/([\\w\\.]+)/i                                           // Avast/AVG Secure Browser\n            ], [[NAME, /(.+)/, '$1 Secure '+BROWSER], VERSION], [\n            /\\bfocus\\/([\\w\\.]+)/i                                               // Firefox Focus\n            ], [VERSION, [NAME, FIREFOX+' Focus']], [\n            /\\bopt\\/([\\w\\.]+)/i                                                 // Opera Touch\n            ], [VERSION, [NAME, OPERA+' Touch']], [\n            /coc_coc\\w+\\/([\\w\\.]+)/i                                            // Coc Coc Browser\n            ], [VERSION, [NAME, 'Coc Coc']], [\n            /dolfin\\/([\\w\\.]+)/i                                                // Dolphin\n            ], [VERSION, [NAME, 'Dolphin']], [\n            /coast\\/([\\w\\.]+)/i                                                 // Opera Coast\n            ], [VERSION, [NAME, OPERA+' Coast']], [\n            /miuibrowser\\/([\\w\\.]+)/i                                           // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI' + SUFFIX_BROWSER]], [\n            /fxios\\/([\\w\\.-]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, FIREFOX]], [\n            /\\bqihoobrowser\\/?([\\w\\.]*)/i                                       // 360\n            ], [VERSION, [NAME, '360']], [\n            /\\b(qq)\\/([\\w\\.]+)/i                                                // QQ\n            ], [[NAME, /(.+)/, '$1Browser'], VERSION], [\n            /(oculus|sailfish|huawei|vivo|pico)browser\\/([\\w\\.]+)/i\n            ], [[NAME, /(.+)/, '$1' + SUFFIX_BROWSER], VERSION], [              // Oculus/Sailfish/HuaweiBrowser/VivoBrowser/PicoBrowser\n            /samsungbrowser\\/([\\w\\.]+)/i                                        // Samsung Internet\n            ], [VERSION, [NAME, SAMSUNG + ' Internet']], [\n            /metasr[\\/ ]?([\\d\\.]+)/i                                            // Sogou Explorer\n            ], [VERSION, [NAME, 'Sogou Explorer']], [\n            /(sogou)mo\\w+\\/([\\d\\.]+)/i                                          // Sogou Mobile\n            ], [[NAME, 'Sogou Mobile'], VERSION], [\n            /(electron)\\/([\\w\\.]+) safari/i,                                    // Electron-based App\n            /(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,                   // Tesla\n            /m?(qqbrowser|2345(?=browser|chrome|explorer))\\w*[\\/ ]?v?([\\w\\.]+)/i   // QQ/2345\n            ], [NAME, VERSION], [\n            /(lbbrowser|rekonq)/i,                                              // LieBao Browser/Rekonq\n            /\\[(linkedin)app\\]/i                                                // LinkedIn App for iOS & Android\n            ], [NAME], [\n            /ome\\/([\\w\\.]+) \\w* ?(iron) saf/i,                                  // Iron\n            /ome\\/([\\w\\.]+).+qihu (360)[es]e/i                                  // 360\n            ], [VERSION, NAME], [\n\n            // WebView\n            /((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i       // Facebook App for iOS & Android\n            ], [[NAME, FACEBOOK], VERSION], [\n            /(Klarna)\\/([\\w\\.]+)/i,                                             // Klarna Shopping Browser for iOS & Android\n            /(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,                             // Kakao App\n            /(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,                                  // Naver InApp\n            /(daum)apps[\\/ ]([\\w\\.]+)/i,                                        // Daum App\n            /safari (line)\\/([\\w\\.]+)/i,                                        // Line App for iOS\n            /\\b(line)\\/([\\w\\.]+)\\/iab/i,                                        // Line App for Android\n            /(alipay)client\\/([\\w\\.]+)/i,                                       // Alipay\n            /(twitter)(?:and| f.+e\\/([\\w\\.]+))/i,                               // Twitter\n            /(chromium|instagram|snapchat)[\\/ ]([-\\w\\.]+)/i                     // Chromium/Instagram/Snapchat\n            ], [NAME, VERSION], [\n            /\\bgsa\\/([\\w\\.]+) .*safari\\//i                                      // Google Search Appliance on iOS\n            ], [VERSION, [NAME, 'GSA']], [\n            /musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i                        // TikTok\n            ], [VERSION, [NAME, 'TikTok']], [\n\n            /headlesschrome(?:\\/([\\w\\.]+)| )/i                                  // Chrome Headless\n            ], [VERSION, [NAME, CHROME+' Headless']], [\n\n            / wv\\).+(chrome)\\/([\\w\\.]+)/i                                       // Chrome WebView\n            ], [[NAME, CHROME+' WebView'], VERSION], [\n\n            /droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i           // Android Browser\n            ], [VERSION, [NAME, 'Android '+BROWSER]], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i       // Chrome/OmniWeb/Arora/Tizen/Nokia\n            ], [NAME, VERSION], [\n\n            /version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i                      // Mobile Safari\n            ], [VERSION, [NAME, 'Mobile Safari']], [\n            /version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i                // Safari & Safari Mobile\n            ], [VERSION, NAME], [\n            /webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i                      // Safari < 3.0\n            ], [NAME, [VERSION, strMapper, oldSafariMap]], [\n\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(navigator|netscape\\d?)\\/([-\\w\\.]+)/i                              // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /(wolvic|librewolf)\\/([\\w\\.]+)/i                                    // Wolvic/LibreWolf\n            ], [NAME, VERSION], [\n            /mobile vr; rv:([\\w\\.]+)\\).+firefox/i                               // Firefox Reality\n            ], [VERSION, [NAME, FIREFOX+' Reality']], [\n            /ekiohf.+(flow)\\/([\\w\\.]+)/i,                                       // Flow\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\\/ ]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror\n            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(firefox)\\/([\\w\\.]+)/i,                                            // Other Firefox-based\n            /(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,                         // Mozilla\n\n            // Other\n            /(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Obigo/Mosaic/Go/ICE/UP.Browser/Ladybird\n            /\\b(links) \\(([\\w\\.]+)/i                                            // Links\n            ], [NAME, [VERSION, /_/g, '.']], [\n            \n            /(cobalt)\\/([\\w\\.]+)/i                                              // Cobalt\n            ], [NAME, [VERSION, /master.|lts./, \"\"]]\n        ],\n\n        cpu : [[\n\n            /\\b((amd|x|x86[-_]?|wow|win)64)\\b/i                                 // AMD64 (x64)\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i,                                                     // IA32 (quicktime)\n            /\\b((i[346]|x)86)(pc)?\\b/i                                          // IA32 (x86)\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            /\\b(aarch64|arm(v?[89]e?l?|_?64))\\b/i                               // ARM64\n            ], [[ARCHITECTURE, 'arm64']], [\n\n            /\\b(arm(v[67])?ht?n?[fl]p?)\\b/i                                     // ARMHF\n            ], [[ARCHITECTURE, 'armhf']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /( (ce|mobile); ppc;|\\/[\\w\\.]+arm\\b)/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            /((ppc|powerpc)(64)?)( mac|;|\\))/i                                  // PowerPC\n            ], [[ARCHITECTURE, /ower/, EMPTY, lowerize]], [\n\n            / sun4\\w[;\\)]/i                                                     // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n\n            /\\b(avr32|ia64(?=;)|68k(?=\\))|\\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\\b|pa-risc)/i\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            ], [[ARCHITECTURE, lowerize]]\n        ],\n\n        device : [[\n\n            //////////////////////////\n            // MOBILES & TABLETS\n            /////////////////////////\n\n            // Samsung\n            /\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]], [\n            /\\b((?:s[cgp]h|gt|sm)-(?![lr])\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,\n            /samsung[- ]((?!sm-[lr])[-\\w]+)/i,\n            /sec-(sgh\\w+)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]], [\n\n            // Apple\n            /(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i                          // iPod/iPhone\n            ], [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]], [\n            /\\((ipad);[-\\w\\),; ]+apple/i,                                       // iPad\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/i,\n            /\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i\n            ], [MODEL, [VENDOR, APPLE], [TYPE, TABLET]], [\n            /(macintosh);/i\n            ], [MODEL, [VENDOR, APPLE]], [\n\n            // Sharp\n            /\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i\n            ], [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]], [\n\n            // Honor\n            /\\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\\)|;)/i\n            ], [MODEL, [VENDOR, HONOR], [TYPE, TABLET]], [\n            /honor([-\\w ]+)[;\\)]/i\n            ], [MODEL, [VENDOR, HONOR], [TYPE, MOBILE]], [\n\n            // Huawei\n            /\\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\\w\\. ]*(?= bui|\\)))\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]], [\n            /(?:huawei)([-\\w ]+)[;\\)]/i,\n            /\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]], [\n\n            // Xiaomi\n            /oid[^\\)]+; (2[\\dbc]{4}(182|283|rp\\w{2})[cgl]|m2105k81a?c)(?: bui|\\))/i,\n            /\\b((?:red)?mi[-_ ]?pad[\\w- ]*)(?: bui|\\))/i                                // Mi Pad tablets\n            ],[[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, TABLET]], [\n\n            /\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,                  // Xiaomi POCO\n            /\\b; (\\w+) build\\/hm\\1/i,                                           // Xiaomi Hongmi 'numeric' models\n            /\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,                             // Xiaomi Hongmi\n            /\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,                   // Xiaomi Redmi\n            /oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,        // Xiaomi Redmi 'numeric' models\n            /\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\\))/i, // Xiaomi Mi\n            / ([\\w ]+) miui\\/v?\\d/i\n            ], [[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, MOBILE]], [\n\n            // OPPO\n            /; (\\w+) bui.+ oppo/i,\n            /\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i\n            ], [MODEL, [VENDOR, OPPO], [TYPE, MOBILE]], [\n            /\\b(opd2(\\d{3}a?))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, strMapper, { 'OnePlus' : ['304', '403', '203'], '*' : OPPO }], [TYPE, TABLET]], [\n\n            // Vivo\n            /vivo (\\w+)(?: bui|\\))/i,\n            /\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i\n            ], [MODEL, [VENDOR, 'Vivo'], [TYPE, MOBILE]], [\n\n            // Realme\n            /\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i\n            ], [MODEL, [VENDOR, 'Realme'], [TYPE, MOBILE]], [\n\n            // Motorola\n            /\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,\n            /\\bmot(?:orola)?[- ](\\w*)/i,\n            /((?:moto(?! 360)[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]], [\n            /\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]], [\n\n            // LG\n            /((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i\n            ], [MODEL, [VENDOR, LG], [TYPE, TABLET]], [\n            /(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,\n            /\\blg[-e;\\/ ]+((?!browser|netcast|android tv|watch)\\w+)/i,\n            /\\blg-?([\\d\\w]+) bui/i\n            ], [MODEL, [VENDOR, LG], [TYPE, MOBILE]], [\n\n            // Lenovo\n            /(ideatab[-\\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\\d{3,4}(?:f[cu]|xu|[av])|yt\\d?-[jx]?\\d+[lfmx])( bui|;|\\)|\\/)/i,\n            /lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\\w- ]+?)|tb[\\w-]{6,7})( bui|;|\\)|\\/)/i\n            ], [MODEL, [VENDOR, LENOVO], [TYPE, TABLET]], [\n\n            // Nokia\n            /(nokia) (t[12][01])/i\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n            /(?:maemo|nokia).*(n900|lumia \\d+|rm-\\d+)/i,\n            /nokia[-_ ]?(([-\\w\\. ]*))/i\n            ], [[MODEL, /_/g, ' '], [TYPE, MOBILE], [VENDOR, 'Nokia']], [\n\n            // Google\n            /(pixel (c|tablet))\\b/i                                             // Google Pixel C/Tablet\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]], [\n            /droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i                         // Google Pixel\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]], [\n\n            // Sony\n            /droid.+; (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i\n            ], [MODEL, [VENDOR, SONY], [TYPE, MOBILE]], [\n            /sony tablet [ps]/i,\n            /\\b(?:sony)?sgp\\w+(?: bui|\\))/i\n            ], [[MODEL, 'Xperia Tablet'], [VENDOR, SONY], [TYPE, TABLET]], [\n\n            // OnePlus\n            / (kb2005|in20[12]5|be20[12][59])\\b/i,\n            /(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i\n            ], [MODEL, [VENDOR, ONEPLUS], [TYPE, MOBILE]], [\n\n            // Amazon\n            /(alexa)webm/i,\n            /(kf[a-z]{2}wi|aeo(?!bc)\\w\\w)( bui|\\))/i,                           // Kindle Fire without Silk / Echo Show\n            /(kf[a-z]+)( bui|\\)).+silk\\//i                                      // Kindle Fire HD\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]], [\n            /((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i                     // Fire Phone\n            ], [[MODEL, /(.+)/g, 'Fire Phone $1'], [VENDOR, AMAZON], [TYPE, MOBILE]], [\n\n            // BlackBerry\n            /(playbook);[-\\w\\),; ]+(rim)/i                                      // BlackBerry PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n            /\\b((?:bb[a-f]|st[hv])100-\\d)/i,\n            /\\(bb10; (\\w+)/i                                                    // BlackBerry 10\n            ], [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]], [\n\n            // Asus\n            /(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, TABLET]], [\n            / (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]], [\n\n            // HTC\n            /(nexus 9)/i                                                        // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n            /(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,                         // HTC\n\n            // ZTE\n            /(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,\n            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i         // Alcatel/GeeksPhone/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            // TCL\n            /droid [\\w\\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\\w*(\\)| bui)/i\n            ], [MODEL, [VENDOR, 'TCL'], [TYPE, TABLET]], [\n\n            // itel\n            /(itel) ((\\w+))/i\n            ], [[VENDOR, lowerize], MODEL, [TYPE, strMapper, { 'tablet' : ['p10001l', 'w7001'], '*' : 'mobile' }]], [\n\n            // Acer\n            /droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            // Meizu\n            /droid.+; (m[1-5] note) bui/i,\n            /\\bmz-([-\\w]{2,})/i\n            ], [MODEL, [VENDOR, 'Meizu'], [TYPE, MOBILE]], [\n                \n            // Ulefone\n            /; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Ulefone'], [TYPE, MOBILE]], [\n\n            // Energizer\n            /; (energy ?\\w+)(?: bui|\\))/i,\n            /; energizer ([\\w ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Energizer'], [TYPE, MOBILE]], [\n\n            // Cat\n            /; cat (b35);/i,\n            /; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Cat'], [TYPE, MOBILE]], [\n\n            // Smartfren\n            /((?:new )?andromax[\\w- ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Smartfren'], [TYPE, MOBILE]], [\n\n            // Nothing\n            /droid.+; (a(?:015|06[35]|142p?))/i\n            ], [MODEL, [VENDOR, 'Nothing'], [TYPE, MOBILE]], [\n\n            // Archos\n            /; (x67 5g|tikeasy \\w+|ac[1789]\\d\\w+)( b|\\))/i,\n            /archos ?(5|gamepad2?|([\\w ]*[t1789]|hello) ?\\d+[\\w ]*)( b|\\))/i\n            ], [MODEL, [VENDOR, 'Archos'], [TYPE, TABLET]], [\n            /archos ([\\w ]+)( b|\\))/i,\n            /; (ac[3-6]\\d\\w{2,8})( b|\\))/i \n            ], [MODEL, [VENDOR, 'Archos'], [TYPE, MOBILE]], [\n\n            // MIXED\n            /(imo) (tab \\w+)/i,                                                 // IMO\n            /(infinix) (x1101b?)/i                                              // Infinix XPad\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\\w]*)/i,\n                                                                                // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron/Infinix/Tecno/Micromax/Advan\n            /; (hmd|imo) ([\\w ]+?)(?: bui|\\))/i,                                // HMD/IMO\n            /(hp) ([\\w ]+\\w)/i,                                                 // HP iPAQ\n            /(microsoft); (lumia[\\w ]+)/i,                                      // Microsoft Lumia\n            /(lenovo)[-_ ]?([-\\w ]+?)(?: bui|\\)|\\/)/i,                          // Lenovo\n            /(oppo) ?([\\w ]+) bui/i                                             // OPPO\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /(kobo)\\s(ereader|touch)/i,                                         // Kobo\n            /(hp).+(touchpad(?!.+tablet)|tablet)/i,                             // HP TouchPad\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(nook)[\\w ]+build\\/(\\w+)/i,                                        // Nook\n            /(dell) (strea[kpr\\d ]*[\\dko])/i,                                   // Dell Streak\n            /(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,                                  // Le Pan Tablets\n            /(trinity)[- ]*(t\\d{3}) bui/i,                                      // Trinity Tablets\n            /(gigaset)[- ]+(q\\w{1,9}) bui/i,                                    // Gigaset Tablets\n            /(vodafone) ([\\w ]+)(?:\\)| bui)/i                                   // Vodafone\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(surface duo)/i                                                    // Surface Duo\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]], [\n            /droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i                                 // Fairphone\n            ], [MODEL, [VENDOR, 'Fairphone'], [TYPE, MOBILE]], [\n            /(u304aa)/i                                                         // AT&T\n            ], [MODEL, [VENDOR, 'AT&T'], [TYPE, MOBILE]], [\n            /\\bsie-(\\w*)/i                                                      // Siemens\n            ], [MODEL, [VENDOR, 'Siemens'], [TYPE, MOBILE]], [\n            /\\b(rct\\w+) b/i                                                     // RCA Tablets\n            ], [MODEL, [VENDOR, 'RCA'], [TYPE, TABLET]], [\n            /\\b(venue[\\d ]{2,7}) b/i                                            // Dell Venue Tablets\n            ], [MODEL, [VENDOR, 'Dell'], [TYPE, TABLET]], [\n            /\\b(q(?:mv|ta)\\w+) b/i                                              // Verizon Tablet\n            ], [MODEL, [VENDOR, 'Verizon'], [TYPE, TABLET]], [\n            /\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i                       // Barnes & Noble Tablet\n            ], [MODEL, [VENDOR, 'Barnes & Noble'], [TYPE, TABLET]], [\n            /\\b(tm\\d{3}\\w+) b/i\n            ], [MODEL, [VENDOR, 'NuVision'], [TYPE, TABLET]], [\n            /\\b(k88) b/i                                                        // ZTE K Series Tablet\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, TABLET]], [\n            /\\b(nx\\d{3}j) b/i                                                   // ZTE Nubia\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, MOBILE]], [\n            /\\b(gen\\d{3}) b.+49h/i                                              // Swiss GEN Mobile\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, MOBILE]], [\n            /\\b(zur\\d{3}) b/i                                                   // Swiss ZUR Tablet\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, TABLET]], [\n            /\\b((zeki)?tb.*\\b) b/i                                              // Zeki Tablets\n            ], [MODEL, [VENDOR, 'Zeki'], [TYPE, TABLET]], [\n            /\\b([yr]\\d{2}) b/i,\n            /\\b(dragon[- ]+touch |dt)(\\w{5}) b/i                                // Dragon Touch Tablet\n            ], [[VENDOR, 'Dragon Touch'], MODEL, [TYPE, TABLET]], [\n            /\\b(ns-?\\w{0,9}) b/i                                                // Insignia Tablets\n            ], [MODEL, [VENDOR, 'Insignia'], [TYPE, TABLET]], [\n            /\\b((nxa|next)-?\\w{0,9}) b/i                                        // NextBook Tablets\n            ], [MODEL, [VENDOR, 'NextBook'], [TYPE, TABLET]], [\n            /\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i                  // Voice Xtreme Phones\n            ], [[VENDOR, 'Voice'], MODEL, [TYPE, MOBILE]], [\n            /\\b(lvtel\\-)?(v1[12]) b/i                                           // LvTel Phones\n            ], [[VENDOR, 'LvTel'], MODEL, [TYPE, MOBILE]], [\n            /\\b(ph-1) /i                                                        // Essential PH-1\n            ], [MODEL, [VENDOR, 'Essential'], [TYPE, MOBILE]], [\n            /\\b(v(100md|700na|7011|917g).*\\b) b/i                               // Envizen Tablets\n            ], [MODEL, [VENDOR, 'Envizen'], [TYPE, TABLET]], [\n            /\\b(trio[-\\w\\. ]+) b/i                                              // MachSpeed Tablets\n            ], [MODEL, [VENDOR, 'MachSpeed'], [TYPE, TABLET]], [\n            /\\btu_(1491) b/i                                                    // Rotor Tablets\n            ], [MODEL, [VENDOR, 'Rotor'], [TYPE, TABLET]], [\n            /((?:tegranote|shield t(?!.+d tv))[\\w- ]*?)(?: b|\\))/i              // Nvidia Tablets\n            ], [MODEL, [VENDOR, NVIDIA], [TYPE, TABLET]], [\n            /(sprint) (\\w+)/i                                                   // Sprint Phones\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, MICROSOFT], [TYPE, MOBILE]], [\n            /droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i             // Zebra\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]], [\n            /droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]], [\n\n            ///////////////////\n            // SMARTTVS\n            ///////////////////\n\n            /smart-tv.+(samsung)/i                                              // Samsung\n            ], [VENDOR, [TYPE, SMARTTV]], [\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, SAMSUNG], [TYPE, SMARTTV]], [\n            /(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i        // LG SmartTV\n            ], [[VENDOR, LG], [TYPE, SMARTTV]], [\n            /(apple) ?tv/i                                                      // Apple TV\n            ], [VENDOR, [MODEL, APPLE+' TV'], [TYPE, SMARTTV]], [\n            /crkey/i                                                            // Google Chromecast\n            ], [[MODEL, CHROME+'cast'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /droid.+aft(\\w+)( bui|\\))/i                                         // Fire TV\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]], [\n            /(shield \\w+ tv)/i                                                  // Nvidia Shield TV\n            ], [MODEL, [VENDOR, NVIDIA], [TYPE, SMARTTV]], [\n            /\\(dtv[\\);].+(aquos)/i,\n            /(aquos-tv[\\w ]+)\\)/i                                               // Sharp\n            ], [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],[\n            /(bravia[\\w ]+)( bui|\\))/i                                              // Sony\n            ], [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]], [\n            /(mi(tv|box)-?\\w+) bui/i                                            // Xiaomi\n            ], [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]], [\n            /Hbbtv.*(technisat) (.*);/i                                         // TechniSAT\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,                          // Roku\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i         // HbbTV devices\n            ], [[VENDOR, trim], [MODEL, trim], [TYPE, SMARTTV]], [\n                                                                                // SmartTV from Unidentified Vendors\n            /droid.+; ([\\w- ]+) (?:android tv|smart[- ]?tv)/i\n            ], [MODEL, [TYPE, SMARTTV]], [\n            /\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i\n            ], [[TYPE, SMARTTV]], [\n\n            ///////////////////\n            // CONSOLES\n            ///////////////////\n\n            /(ouya)/i,                                                          // Ouya\n            /(nintendo) ([wids3utch]+)/i                                        // Nintendo\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n            /droid.+; (shield)( bui|\\))/i                                       // Nvidia Portable\n            ], [MODEL, [VENDOR, NVIDIA], [TYPE, CONSOLE]], [\n            /(playstation \\w+)/i                                                // Playstation\n            ], [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]], [\n            /\\b(xbox(?: one)?(?!; xbox))[\\); ]/i                                // Microsoft Xbox\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]], [\n\n            ///////////////////\n            // WEARABLES\n            ///////////////////\n\n            /\\b(sm-[lr]\\d\\d[0156][fnuw]?s?|gear live)\\b/i                       // Samsung Galaxy Watch\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, WEARABLE]], [\n            /((pebble))app/i,                                                   // Pebble\n            /(asus|google|lg|oppo) ((pixel |zen)?watch[\\w ]*)( bui|\\))/i        // Asus ZenWatch / LG Watch / Pixel Watch\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /(ow(?:19|20)?we?[1-3]{1,3})/i                                      // Oppo Watch\n            ], [MODEL, [VENDOR, OPPO], [TYPE, WEARABLE]], [\n            /(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i                              // Apple Watch\n            ], [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]], [\n            /(opwwe\\d{3})/i                                                     // OnePlus Watch\n            ], [MODEL, [VENDOR, ONEPLUS], [TYPE, WEARABLE]], [\n            /(moto 360)/i                                                       // Motorola 360\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, WEARABLE]], [\n            /(smartwatch 3)/i                                                   // Sony SmartWatch\n            ], [MODEL, [VENDOR, SONY], [TYPE, WEARABLE]], [\n            /(g watch r)/i                                                      // LG G Watch R\n            ], [MODEL, [VENDOR, LG], [TYPE, WEARABLE]], [\n            /droid.+; (wt63?0{2,3})\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // XR\n            ///////////////////\n\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /(pico) (4|neo3(?: link|pro)?)/i                                    // Pico\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /; (quest( \\d| pro)?)/i                                             // Oculus Quest\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // EMBEDDED\n            ///////////////////\n\n            /(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i                              // Tesla\n            ], [VENDOR, [TYPE, EMBEDDED]], [\n            /(aeobc)\\b/i                                                        // Echo Dot\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]], [\n            /(homepod).+mac os/i                                                // Apple HomePod\n            ], [MODEL, [VENDOR, APPLE], [TYPE, EMBEDDED]], [\n            /windows iot/i\n            ], [[TYPE, EMBEDDED]], [\n\n            ////////////////////\n            // MIXED (GENERIC)\n            ///////////////////\n\n            /droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+? mobile safari/i    // Android Phones from Unidentified Vendors\n            ], [MODEL, [TYPE, MOBILE]], [\n            /droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i       // Android Tablets from Unidentified Vendors\n            ], [MODEL, [TYPE, TABLET]], [\n            /\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i                      // Unidentifiable Tablet\n            ], [[TYPE, TABLET]], [\n            /(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i    // Unidentifiable Mobile\n            ], [[TYPE, MOBILE]], [\n            /droid .+?; ([\\w\\. -]+)( bui|\\))/i                                  // Generic Android Device\n            ], [MODEL, [VENDOR, 'Generic']]\n        ],\n\n        engine : [[\n\n            /windows.+ edge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, EDGE+'HTML']], [\n\n            /(arkweb)\\/([\\w\\.]+)/i                                              // ArkWeb\n            ], [NAME, VERSION], [\n\n            /webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i                         // Blink\n            ], [VERSION, [NAME, 'Blink']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\\/([\\w\\.]+)/i, // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna/Servo\n            /ekioh(flow)\\/([\\w\\.]+)/i,                                          // Flow\n            /(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,                           // KHTML/Tasman/Links\n            /(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,                                      // iCab\n\n            /\\b(libweb)/i                                                       // LibWeb\n            ], [NAME, VERSION], [\n            /ladybird\\//i\n            ], [[NAME, 'LibWeb']], [\n\n            /rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i                                     // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows\n            /microsoft (windows) (vista|xp)/i                                   // Windows (iTunes)\n            ], [NAME, VERSION], [\n            /(windows (?:phone(?: os)?|mobile|iot))[\\/ ]?([\\d\\.\\w ]*)/i         // Windows Phone\n            ], [NAME, [VERSION, strMapper, windowsVersionMap]], [\n            /windows nt 6\\.2; (arm)/i,                                          // Windows RT\n            /windows[\\/ ]([ntce\\d\\. ]+\\w)(?!.+xbox)/i,\n            /(?:win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i\n            ], [[VERSION, strMapper, windowsVersionMap], [NAME, 'Windows']], [\n\n            // iOS/macOS\n            /[adehimnop]{4,7}\\b(?:.*os ([\\w]+) like mac|; opera)/i,             // iOS\n            /(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,\n            /cfnetwork\\/.+darwin/i\n            ], [[VERSION, /_/g, '.'], [NAME, 'iOS']], [\n            /(mac os x) ?([\\w\\. ]*)/i,\n            /(macintosh|mac_powerpc\\b)(?!.+haiku)/i                             // Mac OS\n            ], [[NAME, MAC_OS], [VERSION, /_/g, '.']], [\n\n            // Mobile OSes\n            /droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i                    // Android-x86/HarmonyOS\n            ], [VERSION, NAME], [                                               \n            /(ubuntu) ([\\w\\.]+) like android/i                                  // Ubuntu Touch\n            ], [[NAME, /(.+)/, '$1 Touch'], VERSION], [\n                                                                                // Android/Blackberry/WebOS/QNX/Bada/RIM/KaiOS/Maemo/MeeGo/S40/Sailfish OS/OpenHarmony/Tizen\n            /(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen|webos)\\w*[-\\/; ]?([\\d\\.]*)/i\n            ], [NAME, VERSION], [\n            /\\(bb(10);/i                                                        // BlackBerry 10\n            ], [VERSION, [NAME, BLACKBERRY]], [\n            /(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\\/ ]?([\\w\\.]*)/i       // Symbian\n            ], [VERSION, [NAME, 'Symbian']], [\n            /mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i // Firefox OS\n            ], [VERSION, [NAME, FIREFOX+' OS']], [\n            /web0s;.+rt(tv)/i,\n            /\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i                              // WebOS\n            ], [VERSION, [NAME, 'webOS']], [\n            /watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i                              // watchOS\n            ], [VERSION, [NAME, 'watchOS']], [\n\n            // Google Chromecast\n            /crkey\\/([\\d\\.]+)/i                                                 // Google Chromecast\n            ], [VERSION, [NAME, CHROME+'cast']], [\n            /(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i                                  // Chromium OS\n            ], [[NAME, CHROMIUM_OS], VERSION],[\n\n            // Smart TVs\n            /panasonic;(viera)/i,                                               // Panasonic Viera\n            /(netrange)mmh/i,                                                   // Netrange\n            /(nettv)\\/(\\d+\\.[\\w\\.]+)/i,                                         // NetTV\n\n            // Console\n            /(nintendo|playstation) ([wids345portablevuch]+)/i,                 // Nintendo/Playstation\n            /(xbox); +xbox ([^\\);]+)/i,                                         // Microsoft Xbox (360, One, X, S, Series X, Series S)\n\n            // Other\n            /\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,                            // Joli/Palm\n            /(mint)[\\/\\(\\) ]?(\\w*)/i,                                           // Mint\n            /(mageia|vectorlinux)[; ]/i,                                        // Mageia/VectorLinux\n            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,\n                                                                                // Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire\n            /(hurd|linux)(?: arm\\w*| x86\\w*| ?)([\\w\\.]*)/i,                     // Hurd/Linux\n            /(gnu) ?([\\w\\.]*)/i,                                                // GNU\n            /\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i, // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly\n            /(haiku) (\\w+)/i                                                    // Haiku\n            ], [NAME, VERSION], [\n            /(sunos) ?([\\w\\.\\d]*)/i                                             // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n            /((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,                              // Solaris\n            /(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,                                  // AIX\n            /\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, // BeOS/OS2/AmigaOS/MorphOS/OpenVMS/Fuchsia/HP-UX/SerenityOS\n            /(unix) ?([\\w\\.]*)/i                                                // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n    var UAParser = function (ua, extensions) {\n\n        if (typeof ua === OBJ_TYPE) {\n            extensions = ua;\n            ua = undefined;\n        }\n\n        if (!(this instanceof UAParser)) {\n            return new UAParser(ua, extensions).getResult();\n        }\n\n        var _navigator = (typeof window !== UNDEF_TYPE && window.navigator) ? window.navigator : undefined;\n        var _ua = ua || ((_navigator && _navigator.userAgent) ? _navigator.userAgent : EMPTY);\n        var _uach = (_navigator && _navigator.userAgentData) ? _navigator.userAgentData : undefined;\n        var _rgxmap = extensions ? extend(regexes, extensions) : regexes;\n        var _isSelfNav = _navigator && _navigator.userAgent == _ua;\n\n        this.getBrowser = function () {\n            var _browser = {};\n            _browser[NAME] = undefined;\n            _browser[VERSION] = undefined;\n            rgxMapper.call(_browser, _ua, _rgxmap.browser);\n            _browser[MAJOR] = majorize(_browser[VERSION]);\n            // Brave-specific detection\n            if (_isSelfNav && _navigator && _navigator.brave && typeof _navigator.brave.isBrave == FUNC_TYPE) {\n                _browser[NAME] = 'Brave';\n            }\n            return _browser;\n        };\n        this.getCPU = function () {\n            var _cpu = {};\n            _cpu[ARCHITECTURE] = undefined;\n            rgxMapper.call(_cpu, _ua, _rgxmap.cpu);\n            return _cpu;\n        };\n        this.getDevice = function () {\n            var _device = {};\n            _device[VENDOR] = undefined;\n            _device[MODEL] = undefined;\n            _device[TYPE] = undefined;\n            rgxMapper.call(_device, _ua, _rgxmap.device);\n            if (_isSelfNav && !_device[TYPE] && _uach && _uach.mobile) {\n                _device[TYPE] = MOBILE;\n            }\n            // iPadOS-specific detection: identified as Mac, but has some iOS-only properties\n            if (_isSelfNav && _device[MODEL] == 'Macintosh' && _navigator && typeof _navigator.standalone !== UNDEF_TYPE && _navigator.maxTouchPoints && _navigator.maxTouchPoints > 2) {\n                _device[MODEL] = 'iPad';\n                _device[TYPE] = TABLET;\n            }\n            return _device;\n        };\n        this.getEngine = function () {\n            var _engine = {};\n            _engine[NAME] = undefined;\n            _engine[VERSION] = undefined;\n            rgxMapper.call(_engine, _ua, _rgxmap.engine);\n            return _engine;\n        };\n        this.getOS = function () {\n            var _os = {};\n            _os[NAME] = undefined;\n            _os[VERSION] = undefined;\n            rgxMapper.call(_os, _ua, _rgxmap.os);\n            if (_isSelfNav && !_os[NAME] && _uach && _uach.platform && _uach.platform != 'Unknown') {\n                _os[NAME] = _uach.platform  \n                                    .replace(/chrome os/i, CHROMIUM_OS)\n                                    .replace(/macos/i, MAC_OS);           // backward compatibility\n            }\n            return _os;\n        };\n        this.getResult = function () {\n            return {\n                ua      : this.getUA(),\n                browser : this.getBrowser(),\n                engine  : this.getEngine(),\n                os      : this.getOS(),\n                device  : this.getDevice(),\n                cpu     : this.getCPU()\n            };\n        };\n        this.getUA = function () {\n            return _ua;\n        };\n        this.setUA = function (ua) {\n            _ua = (typeof ua === STR_TYPE && ua.length > UA_MAX_LENGTH) ? trim(ua, UA_MAX_LENGTH) : ua;\n            return this;\n        };\n        this.setUA(_ua);\n        return this;\n    };\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER =  enumerize([NAME, VERSION, MAJOR]);\n    UAParser.CPU = enumerize([ARCHITECTURE]);\n    UAParser.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);\n    UAParser.ENGINE = UAParser.OS = enumerize([NAME, VERSION]);\n\n    ///////////\n    // Export\n    //////////\n\n    // check js environment\n    if (typeof(exports) !== UNDEF_TYPE) {\n        // nodejs env\n        if (typeof module !== UNDEF_TYPE && module.exports) {\n            exports = module.exports = UAParser;\n        }\n        exports.UAParser = UAParser;\n    } else {\n        // requirejs env (optional)\n        if (typeof(define) === FUNC_TYPE && define.amd) {\n            define(function () {\n                return UAParser;\n            });\n        } else if (typeof window !== UNDEF_TYPE) {\n            // browser env\n            window.UAParser = UAParser;\n        }\n    }\n\n    // jQuery/Zepto specific (optional)\n    // Note:\n    //   In AMD env the global scope should be kept clean, but jQuery is an exception.\n    //   jQuery always exports to global scope, unless jQuery.noConflict(true) is used,\n    //   and we should catch that.\n    var $ = typeof window !== UNDEF_TYPE && (window.jQuery || window.Zepto);\n    if ($ && !$.ua) {\n        var parser = new UAParser();\n        $.ua = parser.getResult();\n        $.ua.get = function () {\n            return parser.getUA();\n        };\n        $.ua.set = function (ua) {\n            parser.setUA(ua);\n            var result = parser.getResult();\n            for (var prop in result) {\n                $.ua[prop] = result[prop];\n            }\n        };\n    }\n\n})(typeof window === 'object' ? window : this);\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,KAAC,SAAUA,SAAQ,WAAW;AAE1B;AAOA,UAAI,aAAc,UACd,QAAc,IACd,UAAc,KACd,YAAc,YACd,aAAc,aACd,WAAc,UACd,WAAc,UACd,QAAc,SACd,QAAc,SACd,OAAc,QACd,OAAc,QACd,SAAc,UACd,UAAc,WACd,eAAc,gBACd,UAAc,WACd,SAAc,UACd,SAAc,UACd,UAAc,WACd,WAAc,YACd,WAAc,YACd,gBAAgB;AAEpB,UAAI,SAAU,UACV,QAAU,SACV,OAAU,QACV,aAAa,cACb,UAAU,WACV,SAAU,UACV,OAAU,QACV,UAAU,WACV,SAAU,UACV,QAAU,SACV,SAAU,UACV,SAAU,UACV,KAAU,MACV,YAAY,aACZ,WAAY,YACZ,SAAU,UACV,UAAU,WACV,QAAU,SACV,OAAU,QACV,UAAU,WACV,QAAU,SACV,OAAU,QACV,SAAU,UACV,QAAU,SACV,WAAc,YACd,cAAc,eACd,SAAU,UACV,iBAAiB;AAMrB,UAAI,SAAS,SAAUC,UAAS,YAAY;AACpC,YAAI,gBAAgB,CAAC;AACrB,iBAAS,KAAKA,UAAS;AACnB,cAAI,WAAW,CAAC,KAAK,WAAW,CAAC,EAAE,SAAS,MAAM,GAAG;AACjD,0BAAc,CAAC,IAAI,WAAW,CAAC,EAAE,OAAOA,SAAQ,CAAC,CAAC;AAAA,UACtD,OAAO;AACH,0BAAc,CAAC,IAAIA,SAAQ,CAAC;AAAA,UAChC;AAAA,QACJ;AACA,eAAO;AAAA,MACX,GACA,YAAY,SAAU,KAAK;AACvB,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAC7B,gBAAM,IAAI,CAAC,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC;AAAA,QACvC;AACA,eAAO;AAAA,MACX,GACA,MAAM,SAAU,MAAM,MAAM;AACxB,eAAO,OAAO,SAAS,WAAW,SAAS,IAAI,EAAE,QAAQ,SAAS,IAAI,CAAC,MAAM,KAAK;AAAA,MACtF,GACA,WAAW,SAAU,KAAK;AACtB,eAAO,IAAI,YAAY;AAAA,MAC3B,GACA,WAAW,SAAU,SAAS;AAC1B,eAAO,OAAO,YAAa,WAAW,QAAQ,QAAQ,YAAY,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,MAC7F,GACA,OAAO,SAAU,KAAK,KAAK;AACvB,YAAI,OAAO,QAAS,UAAU;AAC1B,gBAAM,IAAI,QAAQ,UAAU,KAAK;AACjC,iBAAO,OAAO,QAAS,aAAa,MAAM,IAAI,UAAU,GAAG,aAAa;AAAA,QAC5E;AAAA,MACR;AAMA,UAAI,YAAY,SAAU,IAAI,QAAQ;AAE9B,YAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,SAAS;AAGhC,eAAO,IAAI,OAAO,UAAU,CAAC,SAAS;AAElC,cAAI,QAAQ,OAAO,CAAC,GAChB,QAAQ,OAAO,IAAI,CAAC;AACxB,cAAI,IAAI;AAGR,iBAAO,IAAI,MAAM,UAAU,CAAC,SAAS;AAEjC,gBAAI,CAAC,MAAM,CAAC,GAAG;AAAE;AAAA,YAAO;AACxB,sBAAU,MAAM,GAAG,EAAE,KAAK,EAAE;AAE5B,gBAAI,CAAC,CAAC,SAAS;AACX,mBAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,wBAAQ,QAAQ,EAAE,CAAC;AACnB,oBAAI,MAAM,CAAC;AAEX,oBAAI,OAAO,MAAM,YAAY,EAAE,SAAS,GAAG;AACvC,sBAAI,EAAE,WAAW,GAAG;AAChB,wBAAI,OAAO,EAAE,CAAC,KAAK,WAAW;AAE1B,2BAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,MAAM,KAAK;AAAA,oBACtC,OAAO;AAEH,2BAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,oBACpB;AAAA,kBACJ,WAAW,EAAE,WAAW,GAAG;AAEvB,wBAAI,OAAO,EAAE,CAAC,MAAM,aAAa,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO;AAExD,2BAAK,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,KAAK,MAAM,OAAO,EAAE,CAAC,CAAC,IAAI;AAAA,oBACxD,OAAO;AAEH,2BAAK,EAAE,CAAC,CAAC,IAAI,QAAQ,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI;AAAA,oBACrD;AAAA,kBACJ,WAAW,EAAE,WAAW,GAAG;AACnB,yBAAK,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,KAAK,MAAM,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI;AAAA,kBAC1E;AAAA,gBACJ,OAAO;AACH,uBAAK,CAAC,IAAI,QAAQ,QAAQ;AAAA,gBAC9B;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,eAAK;AAAA,QACT;AAAA,MACJ,GAEA,YAAY,SAAU,KAAK,KAAK;AAE5B,iBAAS,KAAK,KAAK;AAEf,cAAI,OAAO,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,EAAE,SAAS,GAAG;AACjD,qBAAS,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK;AACpC,kBAAI,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG;AACrB,uBAAQ,MAAM,UAAW,YAAY;AAAA,cACzC;AAAA,YACJ;AAAA,UACJ,WAAW,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG;AACzB,mBAAQ,MAAM,UAAW,YAAY;AAAA,UACzC;AAAA,QACJ;AACA,eAAO,IAAI,eAAe,GAAG,IAAI,IAAI,GAAG,IAAI;AAAA,MACpD;AAOA,UAAI,eAAe;AAAA,QACX,OAAU;AAAA,QACV,OAAU;AAAA,QACV,OAAU;AAAA,QACV,OAAU;AAAA,QACV,SAAU;AAAA,QACV,SAAU;AAAA,QACV,SAAU;AAAA,QACV,KAAU;AAAA,MACd,GACA,oBAAoB;AAAA,QAChB,MAAc;AAAA,QACd,WAAc;AAAA,QACd,UAAc;AAAA,QACd,QAAc;AAAA,QACd,MAAc,CAAC,UAAU,QAAQ;AAAA,QACjC,SAAc;AAAA,QACd,KAAc;AAAA,QACd,KAAc;AAAA,QACd,OAAc;AAAA,QACd,MAAc,CAAC,UAAU,SAAS;AAAA,QAClC,MAAc;AAAA,MACtB;AAMA,UAAI,UAAU;AAAA,QAEV,SAAU;AAAA,UAAC;AAAA,YAEP;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAChC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG9B;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,QAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YACrC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,QAAM,KAAK,CAAC;AAAA,UAAG;AAAA,YACnC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,UAAG;AAAA;AAAA,YAG7B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC/B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,UAAG;AAAA,YACjC;AAAA;AAAA,YACA;AAAA;AAAA;AAAA,YAGA;AAAA;AAAA,YACA;AAAA;AAAA;AAAA,YAGA;AAAA;AAAA,YAEA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC/B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,YAAY,CAAC;AAAA,UAAG;AAAA,YACpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,OAAK,OAAO,CAAC;AAAA,UAAG;AAAA,YACpC;AAAA;AAAA,YACA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAChC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,WAAW,CAAC;AAAA,UAAG;AAAA,YACnC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;AAAA,UAAG;AAAA,YAC5B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAChC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,kBAAgB,OAAO,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,eAAa,OAAO,GAAG,OAAO;AAAA,UAAG;AAAA,YACpD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,UAAQ,QAAQ,CAAC;AAAA,UAAG;AAAA,YACxC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,QAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YACtC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,UAAG;AAAA,YACjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,UAAG;AAAA,YACjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,QAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YACtC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,SAAS,cAAc,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC/B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,UAAG;AAAA,YAC7B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,WAAW,GAAG,OAAO;AAAA,UAAG;AAAA,YAC3C;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,OAAO,cAAc,GAAG,OAAO;AAAA,UAAG;AAAA;AAAA,YACrD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,UAAU,WAAW,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,gBAAgB,CAAC;AAAA,UAAG;AAAA,YACxC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,cAAc,GAAG,OAAO;AAAA,UAAG;AAAA,YACtC;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,IAAI;AAAA,UAAG;AAAA,YACX;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,IAAI;AAAA,UAAG;AAAA;AAAA,YAGpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,GAAG,OAAO;AAAA,UAAG;AAAA,YAChC;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,UAAG;AAAA,YAC7B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAEhC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,SAAO,WAAW,CAAC;AAAA,UAAG;AAAA,YAE1C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,SAAO,UAAU,GAAG,OAAO;AAAA,UAAG;AAAA,YAEzC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,aAAW,OAAO,CAAC;AAAA,UAAG;AAAA,YAE1C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YAEpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,eAAe,CAAC;AAAA,UAAG;AAAA,YACvC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,IAAI;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,CAAC,SAAS,WAAW,YAAY,CAAC;AAAA,UAAG;AAAA,YAE/C;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA;AAAA,YAGpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,UAAU,GAAG,OAAO;AAAA,UAAG;AAAA,YAClC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,UAAQ,UAAU,CAAC;AAAA,UAAG;AAAA,YAC1C;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA;AAAA,YAEA;AAAA;AAAA,YACA;AAAA;AAAA;AAAA,YAGA;AAAA;AAAA,YAEA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,CAAC;AAAA,UAAG;AAAA,YAEjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,CAAC,SAAS,gBAAgB,EAAE,CAAC;AAAA,QAC3C;AAAA,QAEA,KAAM;AAAA,UAAC;AAAA,YAEH;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,UAAG;AAAA,YAE9B;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,MAAM,CAAC;AAAA,UAAG;AAAA,YAE7B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,UAAG;AAAA,YAE9B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,UAAG;AAAA;AAAA,YAG9B;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,KAAK,CAAC;AAAA,UAAG;AAAA,YAE5B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,QAAQ,OAAO,QAAQ,CAAC;AAAA,UAAG;AAAA,YAE9C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,UAAG;AAAA,YAE9B;AAAA;AAAA,UAEA;AAAA,UAAG,CAAC,CAAC,cAAc,QAAQ,CAAC;AAAA,QAChC;AAAA,QAEA,QAAS;AAAA,UAAC;AAAA;AAAA;AAAA;AAAA;AAAA,YAON;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA,YACA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA;AAAA,YACA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,CAAC;AAAA,UAAG;AAAA;AAAA,YAG7B;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG7C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG7C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC9C;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG9C;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAE,CAAC,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAE1D;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG3D;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC5C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,EAAE,WAAY,CAAC,OAAO,OAAO,KAAK,GAAG,KAAM,KAAK,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGpG;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG9C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGhD;AAAA,YACA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAChD;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGhD;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC1C;AAAA,YACA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG1C;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG9C;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACpC;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,MAAM,MAAM,GAAG,CAAC,QAAQ,OAAO,CAAC;AAAA,UAAG;AAAA;AAAA,YAG5D;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC9C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG9C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC5C;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,eAAe,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG/D;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG/C;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC9C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,SAAS,eAAe,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG1E;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,QAAQ,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACpC;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGlD;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC5C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG5C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA;AAAA;AAAA,YAGA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGjD;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG7C;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,QAAQ,GAAG,OAAO,CAAC,MAAM,WAAW,EAAE,UAAW,CAAC,WAAW,OAAO,GAAG,KAAM,SAAS,CAAC,CAAC;AAAA,UAAG;AAAA;AAAA,YAGxG;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG9C;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG/C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGjD;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGnD;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG7C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGnD;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGjD;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAChD;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGhD;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,YAEA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACjD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACnD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC9C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACjD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC9C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACjD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,gBAAgB,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACxD;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAClD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC9C;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,cAAc,GAAG,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACtD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAClD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAClD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,OAAO,GAAG,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,OAAO,GAAG,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACnD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACjD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACnD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC9C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,OAAO,GAAG,GAAG,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC/D;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA;AAAA;AAAA,YAM7C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC9B;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,KAAK,SAAS,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAClE;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YACpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,CAAC,OAAO,QAAM,KAAK,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YACpD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,SAAO,MAAM,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAChE;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAE;AAAA,YAC7C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YACrC;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA;AAAA,YAErD;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC7B;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA;AAAA;AAAA;AAAA,YAMtB;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YACrC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC7C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA;AAAA;AAAA;AAAA,YAMlD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YACjD;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YACtC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAC9C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YACjD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAClD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAC9C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAC5C;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA;AAAA;AAAA;AAAA,YAM/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAChD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YACtC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA;AAAA;AAAA;AAAA,YAMlD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAC/B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAChD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAC/C;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA;AAAA;AAAA;AAAA,YAMvB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC5B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC5B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACrB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACrB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,CAAC;AAAA,QAClC;AAAA,QAEA,QAAS;AAAA,UAAC;AAAA,YAEN;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,OAAK,MAAM,CAAC;AAAA,UAAG;AAAA,YAEnC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YAEpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAE/B;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAEvB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,IAAI;AAAA,QACrB;AAAA,QAEA,IAAK;AAAA,UAAC;AAAA;AAAA,YAGF;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,CAAC,SAAS,WAAW,iBAAiB,CAAC;AAAA,UAAG;AAAA,YACpD;AAAA;AAAA,YACA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,SAAS,WAAW,iBAAiB,GAAG,CAAC,MAAM,SAAS,CAAC;AAAA,UAAG;AAAA;AAAA,YAGjE;AAAA;AAAA,YACA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,SAAS,MAAM,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC;AAAA,UAAG;AAAA,YAC1C;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,MAAM,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC;AAAA,UAAG;AAAA;AAAA,YAG3C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,IAAI;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,UAAU,GAAG,OAAO;AAAA,UAAG;AAAA;AAAA,YAE1C;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,UAAU,CAAC;AAAA,UAAG;AAAA,YAClC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,UAAG;AAAA,YACjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,UAAQ,KAAK,CAAC;AAAA,UAAG;AAAA,YACrC;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAC/B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,UAAG;AAAA;AAAA,YAGjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,SAAO,MAAM,CAAC;AAAA,UAAG;AAAA,YACrC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,WAAW,GAAG,OAAO;AAAA,UAAE;AAAA;AAAA,YAGlC;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA;AAAA,YAGA;AAAA;AAAA,YACA;AAAA;AAAA;AAAA,YAGA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,SAAS,GAAG,OAAO;AAAA,UAAG;AAAA,YACjC;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,QACrB;AAAA,MACJ;AAMA,UAAI,WAAW,SAAU,IAAI,YAAY;AAErC,YAAI,OAAO,OAAO,UAAU;AACxB,uBAAa;AACb,eAAK;AAAA,QACT;AAEA,YAAI,EAAE,gBAAgB,WAAW;AAC7B,iBAAO,IAAI,SAAS,IAAI,UAAU,EAAE,UAAU;AAAA,QAClD;AAEA,YAAI,aAAc,OAAOD,YAAW,cAAcA,QAAO,YAAaA,QAAO,YAAY;AACzF,YAAI,MAAM,OAAQ,cAAc,WAAW,YAAa,WAAW,YAAY;AAC/E,YAAI,QAAS,cAAc,WAAW,gBAAiB,WAAW,gBAAgB;AAClF,YAAI,UAAU,aAAa,OAAO,SAAS,UAAU,IAAI;AACzD,YAAI,aAAa,cAAc,WAAW,aAAa;AAEvD,aAAK,aAAa,WAAY;AAC1B,cAAI,WAAW,CAAC;AAChB,mBAAS,IAAI,IAAI;AACjB,mBAAS,OAAO,IAAI;AACpB,oBAAU,KAAK,UAAU,KAAK,QAAQ,OAAO;AAC7C,mBAAS,KAAK,IAAI,SAAS,SAAS,OAAO,CAAC;AAE5C,cAAI,cAAc,cAAc,WAAW,SAAS,OAAO,WAAW,MAAM,WAAW,WAAW;AAC9F,qBAAS,IAAI,IAAI;AAAA,UACrB;AACA,iBAAO;AAAA,QACX;AACA,aAAK,SAAS,WAAY;AACtB,cAAI,OAAO,CAAC;AACZ,eAAK,YAAY,IAAI;AACrB,oBAAU,KAAK,MAAM,KAAK,QAAQ,GAAG;AACrC,iBAAO;AAAA,QACX;AACA,aAAK,YAAY,WAAY;AACzB,cAAI,UAAU,CAAC;AACf,kBAAQ,MAAM,IAAI;AAClB,kBAAQ,KAAK,IAAI;AACjB,kBAAQ,IAAI,IAAI;AAChB,oBAAU,KAAK,SAAS,KAAK,QAAQ,MAAM;AAC3C,cAAI,cAAc,CAAC,QAAQ,IAAI,KAAK,SAAS,MAAM,QAAQ;AACvD,oBAAQ,IAAI,IAAI;AAAA,UACpB;AAEA,cAAI,cAAc,QAAQ,KAAK,KAAK,eAAe,cAAc,OAAO,WAAW,eAAe,cAAc,WAAW,kBAAkB,WAAW,iBAAiB,GAAG;AACxK,oBAAQ,KAAK,IAAI;AACjB,oBAAQ,IAAI,IAAI;AAAA,UACpB;AACA,iBAAO;AAAA,QACX;AACA,aAAK,YAAY,WAAY;AACzB,cAAI,UAAU,CAAC;AACf,kBAAQ,IAAI,IAAI;AAChB,kBAAQ,OAAO,IAAI;AACnB,oBAAU,KAAK,SAAS,KAAK,QAAQ,MAAM;AAC3C,iBAAO;AAAA,QACX;AACA,aAAK,QAAQ,WAAY;AACrB,cAAI,MAAM,CAAC;AACX,cAAI,IAAI,IAAI;AACZ,cAAI,OAAO,IAAI;AACf,oBAAU,KAAK,KAAK,KAAK,QAAQ,EAAE;AACnC,cAAI,cAAc,CAAC,IAAI,IAAI,KAAK,SAAS,MAAM,YAAY,MAAM,YAAY,WAAW;AACpF,gBAAI,IAAI,IAAI,MAAM,SACG,QAAQ,cAAc,WAAW,EACjC,QAAQ,UAAU,MAAM;AAAA,UACjD;AACA,iBAAO;AAAA,QACX;AACA,aAAK,YAAY,WAAY;AACzB,iBAAO;AAAA,YACH,IAAU,KAAK,MAAM;AAAA,YACrB,SAAU,KAAK,WAAW;AAAA,YAC1B,QAAU,KAAK,UAAU;AAAA,YACzB,IAAU,KAAK,MAAM;AAAA,YACrB,QAAU,KAAK,UAAU;AAAA,YACzB,KAAU,KAAK,OAAO;AAAA,UAC1B;AAAA,QACJ;AACA,aAAK,QAAQ,WAAY;AACrB,iBAAO;AAAA,QACX;AACA,aAAK,QAAQ,SAAUE,KAAI;AACvB,gBAAO,OAAOA,QAAO,YAAYA,IAAG,SAAS,gBAAiB,KAAKA,KAAI,aAAa,IAAIA;AACxF,iBAAO;AAAA,QACX;AACA,aAAK,MAAM,GAAG;AACd,eAAO;AAAA,MACX;AAEA,eAAS,UAAU;AACnB,eAAS,UAAW,UAAU,CAAC,MAAM,SAAS,KAAK,CAAC;AACpD,eAAS,MAAM,UAAU,CAAC,YAAY,CAAC;AACvC,eAAS,SAAS,UAAU,CAAC,OAAO,QAAQ,MAAM,SAAS,QAAQ,SAAS,QAAQ,UAAU,QAAQ,CAAC;AACvG,eAAS,SAAS,SAAS,KAAK,UAAU,CAAC,MAAM,OAAO,CAAC;AAOzD,UAAI,OAAO,YAAa,YAAY;AAEhC,YAAI,OAAO,WAAW,cAAc,OAAO,SAAS;AAChD,oBAAU,OAAO,UAAU;AAAA,QAC/B;AACA,gBAAQ,WAAW;AAAA,MACvB,OAAO;AAEH,YAAI,OAAO,WAAY,aAAa,OAAO,KAAK;AAC5C,iBAAO,WAAY;AACf,mBAAO;AAAA,UACX,CAAC;AAAA,QACL,WAAW,OAAOF,YAAW,YAAY;AAErC,UAAAA,QAAO,WAAW;AAAA,QACtB;AAAA,MACJ;AAOA,UAAI,IAAI,OAAOA,YAAW,eAAeA,QAAO,UAAUA,QAAO;AACjE,UAAI,KAAK,CAAC,EAAE,IAAI;AACZ,YAAI,SAAS,IAAI,SAAS;AAC1B,UAAE,KAAK,OAAO,UAAU;AACxB,UAAE,GAAG,MAAM,WAAY;AACnB,iBAAO,OAAO,MAAM;AAAA,QACxB;AACA,UAAE,GAAG,MAAM,SAAU,IAAI;AACrB,iBAAO,MAAM,EAAE;AACf,cAAI,SAAS,OAAO,UAAU;AAC9B,mBAAS,QAAQ,QAAQ;AACrB,cAAE,GAAG,IAAI,IAAI,OAAO,IAAI;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAAA,IAEJ,GAAG,OAAO,WAAW,WAAW,SAAS,OAAI;AAAA;AAAA;", "names": ["window", "regexes", "ua"]}