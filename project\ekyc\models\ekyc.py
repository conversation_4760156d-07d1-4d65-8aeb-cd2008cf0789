from django.db import models
from django.conf import settings
from django.utils import timezone
from crequest.middleware import CrequestMiddleware
from rest_framework import status
from rest_framework.reverse import reverse
from pydash import get

from .mixins.ekyc_attempt_mixin import EkycAttemptMixin
from .mixins.ekyc_report_mixin import EkycReportMixin
from ..helpers.external_api import compare
from ..helpers.import_callable import import_callable
from ..helpers.sjcl_encryption import encrypt_url
from ..helpers.build_url_from_settings import build_url_from_settings
from ..helpers.get_form import (
    get_form_liveness_item,
    get_form_document_item,
    get_form_backcard_item,
    get_form_settings,
    get_applied_form,
    get_form,
)
from .liveness import (
    Liveness,
    STATUS_PENDING,
    STATUS_SUCCESS,
    STATUS_FAILED_BACKEND,
    STATUS_FAILED_FRONTEND,
    STATUS_CANCELLED,
)
from .face_compare import FaceCompare
from .front_card import FrontCard
from .back_card import BackCard

from webhook.webhook import webhook
import traceback

EKYC_RESULT_API_TIMEOUT = settings.EKYC_RESULT_API_TIMEOUT

ekyc_update_application_status = settings.EKYC_UPDATE_STATUS_TO_REF_METHOD
if ekyc_update_application_status:
    ekyc_update_application_status = import_callable(ekyc_update_application_status)


class Ekyc(models.Model, EkycAttemptMixin, EkycReportMixin):
    ref = models.CharField(max_length=191, unique=True, db_index=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    __applied_form = None
    __form = None
    dynamicform = None

    def save(self, *args, **kwargs):
        self.updated_at = timezone.now()
        super().save(*args, **kwargs)

    @property
    def applied_form(self):
        if not self.__applied_form:
            try:
                self.__applied_form = get_applied_form(self.ref)
            except:
                pass
        return self.__applied_form

    @property
    def form(self):
        if not self.__form:
            if self.applied_form:
                self.__form = self.applied_form.form
            else:
                try:
                    self.__form = get_form(self.ref)
                except:
                    pass
        return self.__form

    @property
    def liveness(self) -> Liveness:
        current_liveness = self.liveness_set.filter(status=STATUS_SUCCESS).order_by("-updated_at").first()
        if not current_liveness:
            return None
        return current_liveness

    @property
    def liveness_upload(self) -> str | None:
        if not self.liveness:
            return None
        return self.liveness.upload_response

    @property
    def liveness_result(self) -> dict | None:
        if not self.liveness:
            return None
        return self.liveness.result_response

    @property
    def liveness_actions(self):
        if not self.liveness:
            return None
        return self.liveness.face_actions

    @property
    def liveness_actions_expire_at(self):
        if not self.liveness:
            return None
        return self.liveness.face_actions_expire_at

    @property
    def latest_liveness(self) -> Liveness:
        return self.liveness_set.filter(upload_response__isnull=False).order_by("-updated_at").first()

    @property
    def success_or_latest_liveness(self) -> Liveness:
        liveness = self.liveness
        if liveness:
            return liveness
        else:
            has_result_status = [STATUS_SUCCESS, STATUS_FAILED_BACKEND]
            return self.liveness_set.filter(status__in=has_result_status).order_by("-updated_at").first()

    @property
    def document(self) -> FrontCard:
        current_front_card = self.frontcard_set.filter(is_success=True).order_by("-updated_at").first()
        if not current_front_card:
            return None
        return current_front_card

    @property
    def frontcard(self) -> FrontCard:
        return self.document

    @property
    def frontcard_upload(self) -> str | None:
        if not self.frontcard:
            return None
        return self.frontcard.upload_response

    @property
    def frontcard_result(self) -> dict | None:
        if not self.frontcard:
            return None
        return self.frontcard.result_response

    @property
    def success_or_latest_frontcard(self) -> FrontCard:
        frontcard = self.frontcard
        if frontcard:
            return frontcard
        else:
            return self.frontcard_set.order_by("-updated_at").first()

    @property
    def latest_frontcard(self) -> FrontCard:
        return self.frontcard_set.filter(upload_response__isnull=False).order_by("-updated_at").first()

    @property
    def backcard(self) -> BackCard:
        current_back_card = self.backcard_set.filter(is_success=True).order_by("-updated_at").first()
        if not current_back_card:
            return None
        return current_back_card

    @property
    def backcard_upload(self):
        if not self.backcard:
            return None
        return self.backcard.upload_response

    @property
    def backcard_result(self):
        if not self.backcard:
            return None
        return self.backcard.result_response

    @property
    def success_or_latest_backcard(self) -> BackCard:
        backcard = self.backcard
        if backcard:
            return backcard
        else:
            return self.backcard_set.order_by("-updated_at").first()

    @property
    def face_compare(self) -> FaceCompare:
        current_face_compare = self.facecompare_set.filter(is_success=True).order_by("-updated_at").first()
        if not current_face_compare:
            return None
        return current_face_compare

    @property
    def face_compare_upload(self):
        if not self.face_compare:
            return None
        return self.face_compare.upload_response

    @property
    def face_compare_result(self):
        if not self.face_compare:
            return None
        return self.face_compare.result_response

    @property
    def is_complete(self):
        return self.liveness_upload != None and self.frontcard_upload != None and self.backcard_upload != None

    @property
    def finished_fields(self):
        return {
            "Liveness": self.liveness_upload != None,
            "FrontCard": self.frontcard_upload != None,
            "BackCard": self.backcard_upload != None,
        }

    @property
    def compare_url(self):
        return build_url_from_settings(
            applied_form=self.applied_form,
            setting_path="ekyc.face_compare.submit_api_host",
            url_name="ekyc:facecompare-submit",
        )

    def get_comparable_pair(self):
        should_compare_failed_liveness = get_form_settings(
            self.form, "ekyc.face_compare.should_compare_failed_liveness", False
        )
        should_compare_failed_document = get_form_settings(
            self.form, "ekyc.face_compare.should_compare_failed_document", False
        )
        if should_compare_failed_liveness:
            liveness = self.latest_liveness
        else:
            liveness = self.liveness
        if should_compare_failed_document:
            document = self.latest_frontcard
        else:
            document = self.frontcard
        return (liveness, document)

    def has_comparable_pair(self):
        liveness, document = self.get_comparable_pair()
        has_pair = liveness != None and document != None
        return has_pair

    def get_children_set(self, model_name: str):
        if model_name == "liveness":
            return self.liveness_set
        elif model_name == "front_card":
            return self.frontcard_set
        elif model_name == "back_card":
            return self.backcard_set

    @staticmethod
    def convert_component_to_field(field, **kwargs):
        if field == None:
            return None
        field_method_map = {
            "Liveness": "Liveness",
            "LivenessV1": "Liveness",
            "LivenessV2": "Liveness",
            "LivenessV3": "Liveness",
            "LivenessV4": "Liveness",
            "LivenessVNext": "Liveness",
            "FrontCard": "FrontCard",
            "Passport": "FrontCard",
            "DriverLicense": "FrontCard",
            "ResidencePermit": "FrontCard",
            "Portrait": "FrontCard",
            "Document": "FrontCard",
            "DocumentVNext": "FrontCard",
            "BackCardVNext": "BackCard",
            "BackCard": "BackCard",
            "FaceCompare": "FaceCompare",
            "FrontBack": "FrontBack",
        }
        return field_method_map.get(field, field)

    @staticmethod
    def get_answer_url_from_ref(ref, field, **kwargs):
        if field == None:
            return None
        request = CrequestMiddleware.get_request()
        field = Ekyc.convert_component_to_field(field)
        raw_url = reverse("ekyc:result-get", kwargs={"ekyc_ref": ref, "field": field}, request=request)
        return encrypt_url(raw_url, obj={"ref": ref, "field": field}, timeout=EKYC_RESULT_API_TIMEOUT)

    def get_answer_url(self, field, **kwargs):
        return Ekyc.get_answer_url_from_ref(self.ref, field)

    @staticmethod
    def get_report_url_from_ref(ref, field, **kwargs):
        if field == None:
            return None
        request = CrequestMiddleware.get_request()
        field = Ekyc.convert_component_to_field(field)
        raw_url = reverse(
            "ekyc:report-result-get",
            kwargs={"ekyc_ref": ref, "field": field},
            request=request,
        )
        return encrypt_url(raw_url, obj={"ref": ref, "field": field}, timeout=EKYC_RESULT_API_TIMEOUT)

    def get_report_url(self, field, **kwargs):
        return Ekyc.get_report_url_from_ref(self.ref, field)

    def find_liveness_with_image_by_status(self, has_result_status, **kwargs):
        if self.liveness:
            return self.liveness

        attempts: list[Liveness] = self.liveness_set.filter(status__in=has_result_status).order_by("-updated_at").all()
        if len(attempts) > 0:
            # If latest attempt with image
            for attempt in attempts:
                if not attempt.result_image_url_raw:
                    attempt.fetch_report(**kwargs)
                if attempt.result_image_url_raw:
                    return attempt
            # If no image found, return latest one
            return attempts[0]
        # No attempt = None
        return None

    def save_perform_face_compare(self, liveness: Liveness, document: FrontCard, response=None):
        if not response:
            return False, False

        facecompare_response = response.get("face_compare")
        if not facecompare_response:
            return False, False

        status_code = facecompare_response.get("status_code") or 200
        if not status.is_success(status_code):
            return True, False

        upload_response = facecompare_response.get("request_id")
        result_response = {
            "result": {
                "result": facecompare_response.get("result"),
            },
            "gateway_result": {
                "result": facecompare_response,
            },
        }

        self.facecompare_set.create(
            liveness=liveness,
            front_card=document,
            upload_response=upload_response,
            result_response=result_response,
            is_success=True,
        )

        return True, True

    def requestFaceCompare(self, liveness: Liveness | None = None, document: FrontCard | None = None):
        api_version = get_form_settings(self.form, "ekyc.face_compare.api_version", None)

        if not liveness or not document:
            liveness, document = self.get_comparable_pair()

        if liveness and document:
            compare_response, api_status = compare(
                str(self.id),
                liveness.upload_response,
                document.upload_response,
                api_version=api_version,
            )
            is_success = status.is_success(api_status)
            facecompare = self.facecompare_set.create(
                liveness=liveness,
                front_card=document,
                upload_response=compare_response,
                is_success=is_success,
            )
            if is_success:
                return compare_response, facecompare
            else:
                return False, facecompare

        return None, None

    def getResultFaceCompare(self, **kwargs):
        score = None
        message_face_compare_fail = None
        face_compare = self.face_compare

        try:
            if not face_compare:
                is_compare_success, _ = self.requestFaceCompare()
                if is_compare_success == False:
                    raise Exception("Error requesting Face Compare service")
                elif is_compare_success == None:
                    raise Exception("Please complete eKYC")
                face_compare = self.face_compare

            if face_compare.result_response:
                result = face_compare.result_response
            else:
                result, _ = face_compare.fetch_report(**kwargs)

            score: float = get(result, ["result", "result", "score"])
            message: str = get(result, ["result", "result", "status"], "")

            try:
                # Update Face compare message based on criteria
                from dynamicform.hook import get_face_compare_criteria, get_status_from_criteria
                from ekyc.helpers.get_form import get_applied_form

                applied_form = get_applied_form(self.ref)
                if applied_form:
                    ekyc_score_criteria = get_face_compare_criteria(applied_form, "webhook_extra")
                    face_compare_message = get_status_from_criteria(score=score, criteria=ekyc_score_criteria)
                    message = face_compare_message
            except Exception as e:
                print("Exception getResultFaceCompare -> get_face_compare_criteria", e)

            if score and isinstance(score, (int, float)):
                score = round(score, 1)
            else:
                raise Exception("Can't get eKYC score")

            if message.lower() in ["ผ่าน", "pass", "match"]:
                message = "match"
            elif message.lower() in ["ไม่ผ่าน", "fail", "not_match", "unmatch", "not match"]:
                message = "unmatch"
            elif message.lower() in ["ต้องตรวจสอบ", "need_review", "need review"]:
                message = "need_review"

            if face_compare.is_success:
                class_style = {
                    "ผ่าน": "has-text-success",
                    "ต้องตรวจสอบ": "has-text-warning",
                    "ไม่ผ่าน": "has-text-danger",
                }
                return {
                    "status": True,
                    "score": score,
                    "message": message,
                    "css_class": get(class_style, message),
                }, face_compare
            else:
                message = face_compare.result_response["error"]
                message_face_compare_fail = message
                raise Exception(message)
        except Exception as error:
            print(error)
            message = {
                "status": False,
                "score": None,
                "message": str(error),
                "css_class": "has-text-danger",
            }
            if not message_face_compare_fail:
                message_face_compare_fail = message
            return message, face_compare

    def getReportFaceCompare(self, **kwargs):
        message, face_compare = self.getResultFaceCompare()
        status = message["status"]
        score = message["score"]
        self.update_status_to_ref(is_success=status, message=message, score=score)
        return message, face_compare

    def getReportWarning(self, success_or_latest_frontcard=None, **kwargs):
        if not success_or_latest_frontcard:
            success_or_latest_frontcard = self.success_or_latest_frontcard
        if not success_or_latest_frontcard:
            return []

        warning_message_map = {}

        def extract_warning_message(key):
            obj = success_or_latest_frontcard.warning.get(key)
            passed = obj.get("pass", False) == True
            message = obj.get("message", "")
            if not passed:
                warning_message_map[key] = {"message": message, "key": key}
            elif key in warning_message_map:
                del warning_message_map[key]

        for key in success_or_latest_frontcard.warning.keys():
            extract_warning_message(key)

        warning_message_list = list(warning_message_map.values())

        return warning_message_list

    def check_liveness(self, is_report=True, schema=None, entry=None, form_items=None, **kwargs):
        if not entry:
            if is_report:
                entry = self.find_liveness_with_image_by_status(
                    has_result_status=[STATUS_SUCCESS, STATUS_FAILED_BACKEND]
                )
            else:
                entry = self.liveness

        if not entry:
            return False

        if schema:
            return entry.check_conditions(schema)
        elif form_items:
            schema = get_form_liveness_item(items=form_items)
            return entry.check_conditions(schema)
        else:
            form = self.form
            if not schema and form:
                schema = get_form_liveness_item(form=form)
            return entry.check_conditions(schema)

    def check_frontcard(self, is_report=True, schema=None):
        if is_report:
            entry = self.success_or_latest_frontcard
        else:
            entry = self.frontcard

        if entry:
            if not schema and self.form:
                schema = get_form_document_item(self.form)

            # Check schema conditions
            pass_coditions = entry.check_conditions(schema)
            if not pass_coditions:
                return False

            # Check NFC
            pass_nfc = entry.check_nfc(schema)
            if not pass_nfc:
                return False

            return True
        else:
            return False

    def check_backcard(self, is_report=True, schema=None):
        if is_report:
            entry = self.success_or_latest_backcard
        else:
            entry = self.backcard

        if entry:
            if not schema and self.form:
                schema = get_form_backcard_item(self.form)
            return entry.check_conditions(schema)
        else:
            return False

    def call_webhook(self, hook_to_call, payload={}):
        try:
            if self.applied_form:
                self.applied_form.request_hook(event_trigger=f"ekyc_{hook_to_call}")
                hook_to_call = hook_to_call.title().replace("_", "")
                event_trigger = f"onEkyc{hook_to_call}"
                self.applied_form.decision_flow_trigger(event=event_trigger, ignore_not_found=True)

            webhook("ekyc", hook_to_call, payload, self.ref)
        except:
            print("Cant fire webhook", "ekyc." + hook_to_call, self.ref)

    def update_status_to_ref(self, is_success, message=None, score=None):
        if not ekyc_update_application_status:
            return
        ekyc_update_application_status(ref=self.ref, ekyc=self, is_success=is_success, message=message, score=score)
        if self.face_compare:
            self.face_compare.call_webhook()

    @staticmethod
    def get_liveness_status(ref, ekyc=None, **kwargs):  # type: ignore

        if not ekyc:
            ekyc: Ekyc = Ekyc.objects.filter(ref=ref).first()

        if not ekyc:
            return None

        is_passed = ekyc.check_liveness(**kwargs)
        if is_passed:
            return "pass"
        else:
            return "fail"

    @staticmethod
    def get_face_compare_extracted_result(
        ref,
        ekyc=None,
        is_report=False,
        dynamicform=None,
        success_or_latest_liveness=None,
        success_or_latest_frontcard=None,
        **kwargs,
    ):
        try:
            if not ekyc:
                ekyc: Ekyc = Ekyc.objects.filter(ref=ref).first()

            result = {
                "status": False,
                "score": None,
                "message": None,
                "css_class": "has-text-danger",
            }
            if ekyc:
                result, _ = ekyc.getResultFaceCompare()
            if not is_report:
                return result

            from dynamicform.submodules.form.models.form import Form

            dynamicform: Form = dynamicform or ekyc.form

            # preview
            liveness_preview = None

            if not success_or_latest_liveness:
                success_or_latest_liveness = ekyc.success_or_latest_liveness

            if success_or_latest_liveness and success_or_latest_liveness.upload_response:
                liveness_preview = success_or_latest_liveness.get_result_image_url(is_report=is_report)

            # preview
            front_card_preview = None

            if not success_or_latest_frontcard:
                success_or_latest_frontcard = ekyc.success_or_latest_frontcard

            if success_or_latest_frontcard and success_or_latest_frontcard.upload_response:
                front_card_preview = success_or_latest_frontcard.get_result_image_url(
                    is_report=is_report, return_face_image=True
                )

            result.update({"front_card_preview": front_card_preview, "liveness_preview": liveness_preview})
            return result

        except Exception as e:
            print("Exception get_face_compare_extracted_result", e)
            traceback.print_exc()
            return {}

    @staticmethod
    def get_face_compare_extracted_result_message(ref, ekyc=None, **kwargs):
        result = Ekyc.get_face_compare_extracted_result(ref, ekyc=ekyc)
        return result["message"]

    @staticmethod
    def get_warning(ref, ekyc=None, success_or_latest_frontcard=None, **kwargs):
        try:
            if ekyc and success_or_latest_frontcard:
                return ekyc.getReportWarning(success_or_latest_frontcard=success_or_latest_frontcard)
            else:
                if not ekyc:
                    ekyc: Ekyc = Ekyc.objects.filter(ref=ref).first()
                if ekyc:
                    return ekyc.getReportWarning(**kwargs)
        except Exception as e:
            print("Exception get_warning", e)
            return []

    @staticmethod
    def get_latest_error(ref, ekyc=None, **kwargs):
        try:
            if not ekyc:
                ekyc: Ekyc = Ekyc.objects.filter(ref=ref).first()
            if ekyc:
                latest_liveness = ekyc.liveness_set.latest("updated_at")
                latest_frontcard = ekyc.frontcard_set.latest("updated_at")

                actions = []
                actions.append(latest_liveness)
                if latest_liveness.updated_at < latest_frontcard.updated_at:
                    actions.insert(0, latest_frontcard)

                for action in actions:
                    latest_error = action.get_latest_error()
                    if len(latest_error) > 0:
                        return latest_error

        except Exception as e:
            print("Exception get_latest_error", e)
            return []

    @staticmethod
    def get_response_from_ref(ref, property, ekyc=None, **kwargs):
        GET_METHOD_MAP = {
            "liveness": Ekyc.get_liveness_status,
            "face_compare": Ekyc.get_face_compare_extracted_result,
            "face_compare_result": Ekyc.get_face_compare_extracted_result_message,
            "warning": Ekyc.get_warning,
            "latest_error": Ekyc.get_latest_error,
        }
        get_method = GET_METHOD_MAP.get(property, ekyc, **kwargs)
        if not get_method:
            return None

        return get_method(ref, ekyc=ekyc)

    @staticmethod
    def get_extra_result(ref: str):
        ekyc: Ekyc = Ekyc.objects.get(ref=ref)

        # Liveness result
        liveness_attempts = ekyc.get_liveness_attempts(is_extra=True)
        liveness_image_url = ""
        liveness_image_raw_url = ""
        if ekyc.liveness:
            # Reuse mage url since it's encrypted & rebuilt every time
            for i, attempt in enumerate(liveness_attempts):
                if attempt["id"] == ekyc.liveness.id:
                    liveness_image_url = get(attempt, ["images", 0, "url"], "")
                    liveness_image_action = get(attempt, ["images", 0, "action"], "")
                    liveness_image_raw_url = ekyc.liveness.get_action_image_url(liveness_image_action)
                    break

        # Front card result
        document_attempts = ekyc.get_document_attempts(is_extra=True)
        document_type = ""
        document_image_url = ""
        document_face_image_url = ""
        raw_document_image_url = ""
        raw_document_face_image_url = ""
        if ekyc.frontcard:
            document_type = ekyc.frontcard.document_type
            # Reuse image url since it's encrypted & rebuilt every time
            for i, attempt in enumerate(document_attempts):
                if attempt["id"] == ekyc.frontcard.id:
                    document_image_url = get(attempt, ["url"], "")
                    break

            # Face image has never been built so we need to fetch it (using i from the loop above to prevent empty attempt url)
            document_face_image_url = ekyc.frontcard.get_result_image_url(
                is_extra=True, return_face_image=True, attempt=i + 1
            )
            raw_document_image_url = ekyc.frontcard.result_image_url_raw
            raw_document_face_image_url = ekyc.frontcard.result_face_image_url_raw

        # Face compare result
        if ekyc.face_compare:
            ekyc.face_compare.fetch_report(is_extra=True)

        face_compare_result = ekyc.face_compare_result
        face_compare_result_score = get(face_compare_result, ["result", "result", "score"])
        face_compare_status = get(face_compare_result, ["result", "result", "status"], "")
        face_compare_status_map = {
            "ไม่ผ่าน": "not_match",
            "ต้องตรวจสอบ": "need_review",
            "ผ่าน": "match",
        }
        face_compare_result_message = face_compare_status_map.get(face_compare_status, "not_compare")

        extra_ekyc = {
            "face_compare": {
                "status": face_compare_result_message,
                "score": face_compare_result_score,
            },
            "liveness": {
                "url": liveness_image_url,
                "attempts": liveness_attempts,
                # Will be used/removed by get_extra
                "raw_url": liveness_image_raw_url,
            },
            "identity_document": {
                "type": document_type,
                "url": document_image_url,
                "face_image_url": document_face_image_url,
                "attempts": document_attempts,
                # Will be used/removed by get_extra
                "raw_url": raw_document_image_url,
                "raw_face_image_url": raw_document_face_image_url,
            },
        }

        # Back card result
        current_back_card: BackCard = ekyc.backcard_set.order_by("-updated_at").first()
        if current_back_card:
            back_card_attempts = ekyc.get_back_card_attempts(is_extra=True)
            back_card_type = current_back_card.document_type
            back_card_image_url = next(
                (get(attempt, ["url"], "") for attempt in back_card_attempts if attempt["id"] == current_back_card.id),
                "",
            )

            extra_ekyc["identity_document_back"] = {
                "type": back_card_type,
                "url": back_card_image_url,
                "attempts": back_card_attempts,
                # Will be used/removed by get_extra
                "raw_url": current_back_card.result_image_url_raw,
            }

        return extra_ekyc

    def set_application_drop_off(self, action):
        if not self.applied_form:
            return

        form = self.applied_form.form
        set_application_drop_off = get_form_settings(form, f"ekyc.set_application_drop_off.{action}", False)
        if not set_application_drop_off:
            return

        self.applied_form.set_drop_off()

        if not self.applied_form.application:
            return

        self.applied_form.application.user_log(
            section=None,
            action=action,
            detail="set drop off",
            old_value=None,
            new_value=None,
        )
